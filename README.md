# Terraform @ Evertrue

This is the repo for our [Terraform](https://www.terraform.io) provisioning scripts.

# What you need

* You will need to [get Terraform](https://www.terraform.io/downloads.html)
* You will also need an AWS profile with write access to the `et-terraform` bucket (used by Terraform to keep state).

# How to use

First a few basics:

> Note that not all services in all environments yet have provisioning scripts built for them so if you don't see something here, chances are it doesn't exist yet and you are going to have to write it yourself.

Once you've got the prerequisites in place, you can change to a service directory (e.g. `prod/services/logs`) and run:
```
terraform state list
```
to see if everything is working.

# How it works

The basic building blocks of Terraform are TF files and var files. One of the most important things to understand is that when you run any `terraform` command, all *.tf files in the current directory are loaded. What this means is that all of the various directories in the `terraform` project directory are actually completely independent of one another unless they are specifically referenced by a `source` flag. The that most common way to pass data between components is with the [input-output](#input-output) system.

Code and settings are declared in TF files and they tend to look like this:
```
# Where to store the backend data for this service
terraform {
  backend "s3" {
    bucket = "et-terraform"
    key = "prod/services/lb.tfstate"
    region = "us-east-1"
  }
}

# Grab data about another service from the remote state store and place it
# in an object called `env` which we will then reference
data "terraform_remote_state" "env" {
  backend = "s3"
  config {
    bucket = "et-terraform"
    region = "us-east-1"
    key = "${var.env}.tfstate"
  }
}

# Define a new object based on the `lb` module. We could actually call it
# anything, but for consistency's sake we call it `lb` here as well

module "lb" {
  source = "../../../modules/lb"

  env = "${var.env}"
  instance_count = 2
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"

  singularity_sg_id = "${var.singularity_sg_id}"
  default_sg_id = "${data.terraform_remote_state.env.default_sg_id}"
  web-services_sg_id = "${data.terraform_remote_state.env.web-services_sg_id}"

  sg_description = "${var.sg_description}"

  private_subnet_ids = "${data.terraform_remote_state.env.private_subnet_ids}"
  vpc_id = "${data.terraform_remote_state.env.vpc_id}"
}
```
The base module for `lb` is actually defined in `modules/lb`. Everything under that directory is sourced and becomes part of the `lb` module here.

Modules provide an easy way to associate a whole bunch of objects (such as security groups) with a service without making it environment specific. Module properties here, seen on the left side of the `=` (e.g. `web-services_sg_id`), are actually defined in `modules/lb/variables.tf`.

## input-output

Objects described in the `outputs.tf` file of one component can be accessed using a block like this in another component:
```
data "terraform_remote_state" "env" {
  backend = "s3"
  config {
    bucket = "et-terraform"
    region = "us-east-1"
    key = "${var.env}.tfstate"
  }
}
```
Note that a `terraform apply` command must first be run on the "sending" component in order for the data to be made available.

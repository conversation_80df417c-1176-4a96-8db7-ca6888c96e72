terraform {
  # S3 Backend is always in the Tools account
  backend "s3" {
    bucket         = "et-devops-tools"
    region         = "us-east-1"
    dynamodb_table = "et-devops-terraform-state-lock"
    acl            = "private"
    key            = "terraform/datadog/us-east-1/terraform.tfstate"
    encrypt        = "true"
    profile        = "evertrue-tools-sso"
    #profile        = "evertruetools"
  }

  required_providers {
    datadog = {
      source  = "DataDog/datadog"
      version = "~> 3.0"
    }
  }
}

provider "datadog" {
  api_key = var.datadog_api_key
  app_key = var.datadog_app_key
  api_url = "https://api.datadoghq.com"
} 
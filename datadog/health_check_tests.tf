module "auth_api_health_check" {
  source = "./modules/health_checks"

  name             = "Auth API"
  url              = "https://api.evertrue.com/auth/health?monitor=datadog"
  service          = "auth-api"
  team             = "platform"
  monitor_priority = 1
}

module "dna_api_health_check" {
  source = "./modules/health_checks"

  name             = "DNA API"
  url              = "https://api.evertrue.com/dna/health?monitor=datadog"
  service          = "dna-api"
  team             = "platform"
  monitor_priority = 1
}

module "contacts_api_health_check" {
  source = "./modules/health_checks"

  name             = "Contacts API"
  url              = "https://api.evertrue.com/contacts/v1/status?oid=1&monitor=datadog"
  service          = "contacts-api"
  team             = "platform"
  monitor_priority = 1
}

module "ugc_api_health_check" {
  source = "./modules/health_checks"

  name             = "UGC API"
  url              = "https://api.evertrue.com/ugc/v1/status?oid=1&monitor=datadog"
  service          = "ugc-api"
  team             = "platform"
  monitor_priority = 2
}

module "assignments_api_health_check" {
  source = "./modules/health_checks"

  name             = "Assignments API"
  url              = "https://api.evertrue.com/assignments-java/v2/status?oid=1&monitor=datadog"
  service          = "assignments-api"
  team             = "platform"
  monitor_priority = 2
}

module "importer_api_health_check" {
  source = "./modules/health_checks"

  name             = "Importer API"
  url              = "https://api.evertrue.com/importer/v1/status?oid=1&monitor=datadog"
  service          = "importer-api"
  team             = "platform"
  monitor_priority = 2
}

module "graphql_health_check" {
  source = "./modules/health_checks"

  name             = "Graphql Server"
  url              = "https://api.evertrue.com/graphql/status?monitor=datadog"
  service          = "graphql-server"
  team             = "platform"
  monitor_priority = 1
}

module "app_evertrue_health_check" {
  source = "./modules/health_checks"

  name             = "App Evertrue"
  url              = "https://app.evertrue.com?monitor=datadog"
  service          = "graphql-server"
  team             = "signal"
  monitor_priority = 1
}

module "pledgemine_health_check" {
  source = "./modules/health_checks"

  name             = "Pledgemine Status"
  url              = "https://pledgemine.com?monitor=datadog"
  service          = "pledgemine-server"
  team             = "cedar"
  monitor_priority = 1
}

module "pledgemine_secure_health_check" {
  source = "./modules/health_checks"

  name             = "Pledgemine Secure Status"
  url              = "https://secure.pledgemine.com?monitor=datadog"
  service          = "pledgemine-server"
  team             = "cedar"
  monitor_priority = 1
}

module "thankview_web_servers_health_check" {
  source = "./modules/health_checks"

  name             = "Thankview Web Servers"
  url              = "https://thankview.com/api/status?monitor=datadog"
  service          = "thankview-web-server"
  team             = "spruce"
  monitor_priority = 1
}

module "fundriver_balance_web_servers_health_check" {
  source = "./modules/health_checks"

  name             = "Fundriver Balance Web Servers"
  url              = "https://etsupport.fundriverbalance.com/Account/Login?ReturnUrl=%2F&monitor=datadog"
  service          = "fundriver-balance-web-server"
  team             = "mahogany"
  monitor_priority = 1
}

module "fundriver_impact_web_servers_health_check" {
  source = "./modules/health_checks"

  name             = "Fundriver Impact Web Servers"
  url              = "https://demo1.fundriverimpact.com/Account/Login?ReturnUrl=%2F&monitor=datadog"
  service          = "fundriver-impact-web-server"
  team             = "mahogany"
  monitor_priority = 1
}

module "fundriver_balance_legacy_web_servers_health_check" {
  source = "./modules/health_checks"

  name             = "Fundriver Balance Legacy Web Servers"
  url              = "https://go.fundriver.com/fdapp/login.aspx?monitor=datadog"
  service          = "fundriver-balance-legacy-web-server"
  team             = "mahogany"
  monitor_priority = 1
}

variable "name" {
  description = "Name of the synthetic test"
  type        = string
}

variable "url" {
  description = "URL to test"
  type        = string
}

variable "service" {
  description = "Service name (e.g., auth-api, nango-api)"
  type        = string
}

variable "team" {
  description = "Team responsible for the service"
  type        = string
  default     = "platform"
}

variable "env" {
  description = "Environment (e.g., prod, stage)"
  type        = string
  default     = "prod"
}

variable "response_time_threshold" {
  description = "Maximum acceptable response time in milliseconds"
  type        = number
  default     = 1000
}

variable "check_interval" {
  description = "How often to run the test in seconds"
  type        = number
  default     = 300
}

variable "locations" {
  description = "Locations to run the test from"
  type        = list(string)
  default     = ["aws:us-east-1"]
}

variable "monitor_priority" {
  description = "Priority level for the monitor (1-5, where 1 is highest priority). If not set, no priority will be assigned."
  type        = number
  nullable    = true
  default     = null
}

locals {
  priority_map = {
    1 = "p1"
    2 = "p2"
    3 = "p3"
    4 = "p4"
    5 = "p5"
  }
  monitor_priority = try(local.priority_map[var.monitor_priority], null)
}

resource "datadog_synthetics_test" "health_check" {
  name    = "${var.name} Health Check Test"
  type    = "api"
  subtype = "http"
  status  = "live"

  request_definition {
    method = "GET"
    url    = var.url
  }

  assertion {
    type     = "statusCode"
    operator = "is"
    target   = "200"
  }

  assertion {
    type     = "responseTime"
    operator = "lessThan"
    target   = var.response_time_threshold
  }

  locations = var.locations

  options_list {
    monitor_priority     = var.monitor_priority == null ? null : var.monitor_priority
    tick_every           = var.check_interval
    min_failure_duration = 0
    min_location_failed  = 1
    retry {
      count    = 1
      interval = var.check_interval
    }
  }

  tags = [
    "env:${var.env}",
    "service:${var.service}",
    "type:api",
    "team:${var.team}",
    "team:devops-infra"
  ]
}

resource "datadog_monitor" "health_check" {
  name     = "${var.name} Health Check Alert"
  type     = "metric alert"
  priority = local.monitor_priority

  # EXACTLY like the working one: count failed Synthetic runs for THIS test over 15m.
  query = "sum(last_15m):sum:synthetics.test_runs{test_public_id:${datadog_synthetics_test.health_check.id},result:failed}.as_count() >= 2"

  # Keep thresholds in Terraform to match the UI (warn at 1, alert at 2)
  monitor_thresholds {
    warning  = 1
    critical = 2
  }

  message = <<-EOT
    {{#is_warning}}${var.name}: 1 failed Synthetic run in the last 15 minutes.{{/is_warning}}
    {{#is_alert}}${var.name}: 2+ failed Synthetic runs in the last 15 minutes.{{/is_alert}}
    @slack-platform {{#is_alert}}@oncall-devops{{/is_alert}}
  EOT

  evaluation_delay    = var.check_interval
  include_tags        = true
  require_full_window = false
  on_missing_data     = "resolve"

  tags = [
    "env:${var.env}",
    "service:${var.service}",
    "type:api",
    "team:${var.team}",
    "team:devops-infra"
  ]
}

# Common tags for all resources
locals {
  common_tags = [
    "service:pledgemine",
    "created_by:micha<PERSON>_d",
    "managed_by:terraform"
  ]
}

# Datadog Monitor for IIS Long-Running Requests
resource "datadog_monitor" "pledge_mine_prod_monitor" {
  name         = "IIS Long-Running Requests Alert"
  type         = "metric alert"
  message      = <<-EOT
{{#is_alert}}
🚨 **IIS Alert: Long-Running Requests Detected!**

**Server:** {{host.name}}
**Count:** {{value}} requests running over 10 minutes
**Time:** {{date}}

**Action Required:** Check IIS server for stuck or slow requests that may be impacting performance.

{{/is_alert}}

{{#is_recovery}}
✅ **RESOLVED:** All IIS requests back to normal (under 10 minutes)
**Server:** {{host.name}}
**Recovery Time:** {{date}}
{{/is_recovery}}

${join(" ", var.notification_channels)}
EOT

  query = "avg(last_5m):avg:iis.requests.long_running{host:EC2AMAZ-4L0RGK7} > 0"

  monitor_thresholds {
    critical = 0
  }

  notify_audit        = false
  timeout_h           = 0
  include_tags        = true
  no_data_timeframe   = 10
  notify_no_data      = true
  renotify_interval   = 0

  tags = [
    "service:iis",
    "environment:production",
    "team:infrastructure",
    "server:EC2AMAZ-4L0RGK7"
  ]
}

# Variable for notification channels
variable "notification_channels" {
  description = "List of notification channels for the IIS monitor"
  type        = list(string)
  default = [
    "@slack-alerts",
    "@email-oncall"
  ]
}
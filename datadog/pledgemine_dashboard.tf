# Dashboard-specific tags
locals {
  dashboard_tags = [
    "service:pledgemine",
    "created_by:micha<PERSON>_d",
    "managed_by:terraform"
  ]
}

# IIS Monitoring Dashboard
resource "datadog_dashboard" "pledge_mine_iis_dashboard" {
  title         = "PledgeMine - IIS Request Monitoring"
  description   = "Dashboard monitoring IIS request performance and long-running requests"
  layout_type   = "ordered"

  # Long-Running Requests Alert Widget
  widget {
    alert_graph_definition {
      alert_id = datadog_monitor.pledge_mine_prod_monitor.id
      viz_type = "timeseries"
      title    = "Long-Running Requests Alert Status"
      live_span = "1h"
    }
  }

  # Current In-Flight Requests
  widget {
    timeseries_definition {
      title = "Current In-Flight Requests"
      show_legend = false
      legend_size = "0"
      
      request {
        q = "avg:iis.requests.in_flight{host:EC2AMAZ-4L0RGK7}"
        display_type = "line"
        style {
          palette = "blue"
          line_type = "solid"
          line_width = "normal"
        }
      }
      
      yaxis {
        min = "0"
      }
    }
  }

  # Long-Running Requests Count
  widget {
    timeseries_definition {
      title = "Long-Running Requests (>10min)"
      show_legend = false
      legend_size = "0"
      
      request {
        q = "avg:iis.requests.long_running{host:EC2AMAZ-4L0RGK7}"
        display_type = "bars"
        style {
          palette = "warm"
          line_type = "solid"
          line_width = "normal"
        }
      }
      
      yaxis {
        min = "0"
      }
    }
  }

  # Max Request Duration
  widget {
    timeseries_definition {
      title = "Max Request Duration (seconds)"
      show_legend = false
      legend_size = "0"
      
      request {
        q = "avg:iis.requests.max_duration{host:EC2AMAZ-4L0RGK7}"
        display_type = "line"
        style {
          palette = "purple" 
          line_type = "solid"
          line_width = "normal"
        }
      }
      
      yaxis {
        min = "0"
      }
    }
  }

  # Current Values Widget - Single Value
  widget {
    query_value_definition {
      title = "Current In-Flight Requests"
      title_size = "16"
      title_align = "left"
      
      request {
        q = "avg:iis.requests.in_flight{host:EC2AMAZ-4L0RGK7}"
        aggregator = "avg"
      }
      
      autoscale = true
      precision = 0
    }
  }

  # Long-Running Requests - Single Value
  widget {
    query_value_definition {
      title = "Long-Running Requests"
      title_size = "16"  
      title_align = "left"
      
      request {
        q = "avg:iis.requests.long_running{host:EC2AMAZ-4L0RGK7}"
        aggregator = "avg"
      }
      
      autoscale = true
      precision = 0
    }
  }

  # Long-Running Request Events (Counter)
  widget {
    timeseries_definition {
      title = " Long-Running Request Events Rate"
      show_legend = false
      legend_size = "0"
      
      request {
        q = "per_minute(avg:iis.requests.long_running.events{host:EC2AMAZ-4L0RGK7}.as_rate())"
        display_type = "bars"
        style {
          palette = "orange"
          line_type = "solid" 
          line_width = "normal"
        }
      }
      
      yaxis {
        min = "0"
      }
    }
  }

  # Monitoring Health Check
  widget {
    query_value_definition {
      title = "Monitoring Health (Errors/min)"
      title_size = "16"
      title_align = "left"
      
      request {
        q = "per_minute(avg:iis.monitoring.errors{host:EC2AMAZ-4L0RGK7}.as_rate())"
        aggregator = "avg"
      }
      
      autoscale = true
      precision = 2
    }
  }


}

# Output the dashboard URL
output "iis_dashboard_url" {
  description = "URL to the IIS monitoring dashboard"
  value       = datadog_dashboard.pledge_mine_iis_dashboard.url
}
# ---------- Signal Uptime SLO (by monitor uptime) ----------
locals {
  signal_monitor_ids = [
    175307805, # App Evertrue Health Check Alert
    175304847, # Assignments API Health Check Alert
    174997548, # Auth API Health Check Alert
    174999655, # Contacts API Health Check Alert
    174999196, # DNA API Health Check Alert
    175306267, # Graphql Server Health Check Alert
    175307512, # Importer API Health Check Alert
    175304583, # UGC API Health Check Alert
  ]
}

resource "datadog_service_level_objective" "signal_uptime" {
  name        = "Signal Uptime"
  description = "Overall uptime of Signal via 8 synthetic health-check monitors."
  type        = "monitor"

  monitor_ids = local.signal_monitor_ids

  # Targets for 7/30/90 days (warning above target)
  thresholds {
    timeframe = "7d"
    target    = 99.9
    warning   = 99.95
  }
  thresholds {
    timeframe = "30d"
    target    = 99.9
    warning   = 99.95
  }
  thresholds {
    timeframe = "90d"
    target    = 99.9
    warning   = 99.95
  }

  tags = [
    "team:platform",
    "service:signal",
    "env:prod",
    "slo:signal-uptime",
  ]
}

# ---------- Signal Uptime dashboard (7/30/90 day view of the SLO) ----------
# Using JSON form avoids provider-version quirks with typed SLO widgets.
resource "datadog_dashboard_json" "signal_uptime" {
  dashboard = jsonencode({
    title              = "Signal Uptime"
    description        = "Uptime from 8 synthetic health-check monitors."
    layout_type        = "ordered"
    reflow_type        = "fixed"
    template_variables = []
    widgets = [
      {
        layout = { x = 0, y = 0, width = 12, height = 8 } # <= 12 cols
        definition = {
          type              = "slo"
          title             = "Signal Uptime (7/30/90d)"
          title_size        = "16"
          title_align       = "left"
          slo_id            = datadog_service_level_objective.signal_uptime.id
          view_type         = "detail"
          view_mode         = "overall" # or "component" for per-monitor focus
          show_error_budget = true
          time_windows      = ["7d", "30d", "90d"]
        }
      }
    ]
  })
}

# ==============================================================================
# THANKVIEW APPLICATION MONITORING ALERTS
# ==============================================================================

# Common tags for all ThankView monitors
locals {
  common_tags = [
    "service:thankview",
    "created_by:michael_d",
    "managed_by:terraform"
  ]
}

# Video Processing Error Rate - Based on APM traces
resource "datadog_monitor" "video_processing_errors" {
  name    = "ThankView - Video Processing Errors"
  type    = "metric alert"
  message = <<-EOT
    **Alert**: ThankView video queue processing is experiencing high error rate

    **Impact**: Video queue jobs are failing - users may not receive processed videos
    **Action**: Check Laravel queue fire errors for thankview-video-queue

    Queue: thankview-video-queue
    Metric: trace.laravel.queue.fire.errors

    ${join(" ", var.notification_channels.critical)}
  EOT

  query = "sum(last_10m):sum:trace.laravel.queue.fire.errors{env:prod,peer.messaging.destination:https://sqs.us-east-1.amazonaws.com/019190539304/thankview-video-queue,service:thankview}.as_rate() > 5"

  monitor_thresholds {
    critical          = 5
    critical_recovery = 3
    warning           = 2
    warning_recovery  = 1
  }

  on_missing_data     = "resolve" # Resolve alert when missing data (no errors = good)
  require_full_window = false

  tags = concat(local.common_tags, ["alert_type:video_errors", "severity:critical"])
}

# Video Processing Latency Monitor - Based on APM traces
resource "datadog_monitor" "video_processing_latency" {
  name    = "ThankView - Video Processing Latency High"
  type    = "metric alert"
  message = <<-EOT
    **Alert**: ThankView video queue processing latency is high

    **Impact**: Users experiencing slow video processing - may think app is broken
    **Action**: Check video processing performance and server resources

    Queue: thankview-video-queue
    Metric: trace.laravel.queue.fire (p75 latency)

    ${join(" ", var.notification_channels.warning)}
  EOT

  query = "avg(last_15m):p75:trace.laravel.queue.fire{env:prod,peer.messaging.destination:https://sqs.us-east-1.amazonaws.com/019190539304/thankview-video-queue,service:thankview} > 480"

  monitor_thresholds {
    critical          = 480 # 8 minutes
    critical_recovery = 300 # 5 minutes
    warning           = 240 # 4 minutes
    warning_recovery  = 150 # 2.5 minutes
  }

  notify_no_data      = false
  require_full_window = false

  tags = concat(local.common_tags, ["alert_type:video_latency", "severity:warning"])
}

# Mail Queue Processing Time Monitor
resource "datadog_monitor" "thankview_mail_queue_processing_time" {
  name    = "ThankView Mail Queue Processing Time"
  type    = "metric alert"
  message = <<-EOT
    Mail queue processing time is elevated.

    **Current Status**: {{#is_alert}} CRITICAL{{/is_alert}}{{#is_warning}} WARNING{{/is_warning}}
    **Value**: {{value}} seconds

    **Historical Context**:
    - Normal average: ~13 seconds
    - Historical max: 46.8 seconds
    - Recent range: 10-30 seconds

    **Impact**: Delayed email delivery to users

    **Runbook**: Check mail queue health, SQS metrics, and email service status

    ${join(" ", var.notification_channels.warning)}
  EOT

  query = "avg(last_5m):p75:trace.laravel.queue.process{env:prod,peer.messaging.destination:https://sqs.us-east-1.amazonaws.com/019190539304/thankview-mail-queue,peer.messaging.system:laravel,service:thankview} by {peer.messaging.destination} > 600"

  monitor_thresholds {
    critical          = 600 # 10 minutes
    critical_recovery = 400 # 6.7 minutes
    warning           = 240 # 4 minutes
    warning_recovery  = 150 # 2.5 minutes
  }

  notify_no_data      = false
  require_full_window = false

  tags = concat(local.common_tags, ["alert_type:mail_latency", "severity:warning"])
}

# Mail Queue Error Rate Monitor
resource "datadog_monitor" "thankview_mail_queue_errors" {
  name    = "ThankView Mail Queue Errors"
  type    = "metric alert"
  message = <<-EOT
    Mail queue is experiencing errors.

    **Current Status**: {{#is_alert}} CRITICAL{{/is_alert}}{{#is_warning}} WARNING{{/is_warning}}
    **Error Count**: {{value}} errors in last 5 minutes

    **Historical Context**:
    - Normal state: 0 errors
    - Occasional spikes: 1-2 errors

    **Impact**: Email delivery failures - users not receiving emails

    **Immediate Actions**:
    1. Check SQS dead letter queue
    2. Review Laravel logs for email errors
    3. Verify email service connectivity
    4. Check for malformed email addresses

    ${join(" ", var.notification_channels.critical)}
  EOT

  query = "sum(last_5m):sum:trace.laravel.queue.process.errors{env:prod,peer.messaging.destination:https://sqs.us-east-1.amazonaws.com/019190539304/thankview-mail-queue,peer.messaging.system:laravel,service:thankview} by {peer.messaging.destination} > 10"

  monitor_thresholds {
    critical          = 10 # 10+ errors in 5 minutes indicates serious issue
    critical_recovery = 5  # Recover when < 5 errors
    warning           = 5  # 5+ errors in 5 minutes warrants investigation
    warning_recovery  = 2  # Recover when < 2 error
  }

  on_missing_data     = "resolve" # Resolve alert when missing data (no errors = good)
  require_full_window = false

  tags = concat(local.common_tags, ["alert_type:mail_errors", "severity:critical"])
}

# Laravel HTTP Request Errors Monitor
# Monitors user-facing web/API request failures (404s, 500s, auth errors)
resource "datadog_monitor" "laravel_request_errors" {
  name    = "ThankView - Laravel HTTP Request Errors"
  type    = "metric alert"
  message = <<-EOT
    Laravel HTTP requests are failing - users experiencing errors.

    **What this monitors**: Web page and API request failures
    **Impact**: Users see error pages, can't access features
    **Common causes**: Database issues, broken code, missing resources

    **Immediate actions**:
    1. Check Laravel logs for specific error details
    2. Verify database connectivity
    3. Check recent deployments
    4. Monitor user reports

    ${join(" ", var.notification_channels.critical)}
  EOT

  query = "sum(last_10m):sum:trace.laravel.request.errors{env:prod,service:thankview}.as_rate() > 10"

  monitor_thresholds {
    critical          = 10 # 10 errors per minute - serious user impact
    critical_recovery = 5  # Recover when < 5 errors per minute
    warning           = 5  # 5 errors per minute - investigate
    warning_recovery  = 2  # Recover when < 2 errors per minute
  }

  on_missing_data     = "resolve" # Resolve alert when missing data (no errors = good)
  require_full_window = false

  tags = concat(local.common_tags, ["alert_type:http_errors", "severity:critical"])
}

# Laravel HTTP Request Volume Monitor
# Monitors overall traffic patterns and detects unusual spikes/drops
resource "datadog_monitor" "laravel_request_hits" {
  name    = "ThankView - Laravel HTTP Request Volume Anomaly"
  type    = "query alert"
  message = <<-EOT
    Unusual HTTP request volume detected.

    **What this monitors**: Total web/API traffic patterns
    **Impact**: Traffic spike (overload) or drop (outage/user issues)
    **Use for**: Capacity planning, incident detection

    **Actions**:
    - High traffic: Check server resources, scaling
    - Low traffic: Verify application availability, marketing campaigns

    ${join(" ", var.notification_channels.warning)}
  EOT

  query = "avg(last_4h):anomalies(sum:trace.laravel.request.hits{env:prod,service:thankview}.as_rate(), 'basic', 2, direction='both', alert_window='last_15m', interval=60, count_default_zero='true') >= 1"

  notify_no_data      = false
  require_full_window = false

  tags = concat(local.common_tags, ["alert_type:traffic_anomaly", "severity:warning"])
}

# Laravel Artisan Command Errors Monitor
# Monitors CLI command failures (scheduled tasks, maintenance commands)
resource "datadog_monitor" "laravel_artisan_errors" {
  name    = "ThankView - Laravel Artisan Command Errors"
  type    = "metric alert"
  message = <<-EOT
    Laravel Artisan commands are failing.

    **What this monitors**: CLI command failures (cron jobs, maintenance tasks)
    **Impact**: Background maintenance broken, scheduled tasks not running
    **Common causes**: Database issues, file permissions, memory limits

    **Examples of failing commands**:
    - php artisan schedule:run (cron jobs)
    - php artisan queue:work (queue workers)
    - php artisan cache:clear (maintenance)

    **Actions**:
    1. Check Laravel logs for command details
    2. Verify cron job configuration
    3. Check server resources (disk, memory)
    4. Test commands manually

    ${join(" ", var.notification_channels.warning)}
  EOT

  query = "sum(last_15m):sum:trace.laravel.artisan.errors{env:prod,service:thankview} > 5"

  monitor_thresholds {
    critical          = 5 # 5 artisan errors in 15 minutes
    critical_recovery = 2 # Recover when < 2 errors
    warning           = 3 # 3 artisan errors in 15 minutes
    warning_recovery  = 1 # Recover when < 1 error
  }

  on_missing_data     = "resolve" # Resolve alert when missing data (no errors = good)
  require_full_window = false

  tags = concat(local.common_tags, ["alert_type:artisan_errors", "severity:warning"])
}

# Laravel Queue Action Errors Monitor
# Monitors general queue system failures (job processing issues)
resource "datadog_monitor" "laravel_queue_action_errors" {
  name    = "ThankView - Laravel Queue Action Errors"
  type    = "metric alert"
  message = <<-EOT
    Laravel queue system experiencing processing errors.

    **What this monitors**: General queue job processing failures
    **Impact**: Background jobs failing, features not working properly
    **Common causes**: Queue worker crashes, job timeouts, resource limits

    **Difference from queue.fire.errors**: This catches broader queue system issues

    **Actions**:
    1. Check queue worker status
    2. Review failed jobs in Laravel Horizon/queue dashboard
    3. Check SQS dead letter queues
    4. Verify queue worker memory/timeout settings

    ${join(" ", var.notification_channels.critical)}
  EOT

  query = "sum(last_10m):sum:trace.laravel.queue.action.errors{env:prod,service:thankview} > 10"

  monitor_thresholds {
    critical          = 10 # 10 queue action errors in 10 minutes
    critical_recovery = 5  # Recover when < 5 errors
    warning           = 5  # 5 queue action errors in 10 minutes
    warning_recovery  = 2  # Recover when < 2 errors
  }

  on_missing_data     = "resolve" # Resolve alert when missing data (no errors = good)
  require_full_window = false

  tags = concat(local.common_tags, ["alert_type:queue_action_errors", "severity:critical"])
}

# Laravel Queue Fire Errors Monitor (All Queues)
# Monitors job execution failures across all queue types
resource "datadog_monitor" "laravel_queue_fire_errors_all" {
  name    = "ThankView - Laravel Queue Fire Errors (All Queues)"
  type    = "metric alert"
  message = <<-EOT
    Laravel queue jobs failing during execution across all queues.

    **What this monitors**: Job execution failures when jobs are "fired" (run)
    **Impact**: Background features broken (emails, video processing, notifications)
    **Scope**: All queues combined (broader than specific video/mail monitors)

    **Common causes**:
    - Jobs throwing exceptions during execution
    - External service timeouts (email, video processing)
    - Invalid data passed to jobs
    - Resource constraints (memory, disk)

    **This complements your specific monitors**:
    - Video queue errors (specific to video processing)
    - Mail queue errors (specific to email delivery)
    - This catches errors in other queues (notifications, cleanup, etc.)

    **Actions**:
    1. Check Laravel Horizon for failed job details
    2. Review job-specific logs
    3. Check external service status
    4. Verify job payload data integrity

    ${join(" ", var.notification_channels.critical)}
  EOT

  query = "sum(last_10m):sum:trace.laravel.queue.fire.errors{env:prod,service:thankview} > 15"

  monitor_thresholds {
    critical          = 15 # 15 job execution errors in 10 minutes
    critical_recovery = 8  # Recover when < 8 errors
    warning           = 8  # 8 job execution errors in 10 minutes
    warning_recovery  = 4  # Recover when < 4 errors
  }

  on_missing_data     = "resolve" # Resolve alert when missing data (no errors = good)
  require_full_window = false

  tags = concat(local.common_tags, ["alert_type:queue_fire_errors", "severity:critical"])
}

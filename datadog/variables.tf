variable "datadog_api_key" {
  description = "Datadog API key"
  type        = string
  sensitive   = true
  default     = "a0a430aee8c5df36cc68fdb1644e3a56"
}

variable "datadog_app_key" {
  description = "Datadog App key"
  type        = string
  sensitive   = true
  default     = "5e2d50f804495a7858c57d01aecc0c9a4d4cda0b"
}

variable "notification_channels" {
  description = "Notification channels for alerts"
  type = object({
    critical = list(string)
    warning  = list(string)
  })
  default = {
    critical = ["@<EMAIL>"]
    warning  = ["@<EMAIL>"]
  }
}
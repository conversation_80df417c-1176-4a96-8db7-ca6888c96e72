data "terraform_remote_state" "master_state" {
  backend = "s3"
  config {
    bucket = "${var.tf_s3_bucket}"
    region = "${var.region}"
    key = "${var.master_state_file}"
  }
}

# data "prod_remote_state" "prod_state" {
#   backend = "s3"
#   config {
#     bucket = "${var.tf_s3_bucket}"
#     region = "${var.region}"
#     key = "terraform-prod.tfstate"
#   }
# }

module "vpc" {
  source = "../modules/vpc"

  cidr = "${var.cidr}"
  enable_dns_support = "${var.enable_dns_support}"
  enable_dns_hostnames = "${var.enable_dns_hostnames}"
  public_ranges = "${var.public_ranges}"
  private_ranges = "${var.private_ranges}"
  office_cidr_block = "${var.office_cidr_block}"
  road_warrior_vpn_cidr_blocks = "${var.road_warrior_vpn_cidr_blocks}"
  road_warrior_vpn_instance_id = "${var.road_warrior_vpn_instance_id}"
  vpn_az = "us-east-1c"
  azs = "${var.azs}"
  env = "${var.env}"
}

resource "aws_vpc_peering_connection" "dev-stage" {
  vpc_id = "${module.vpc.vpc_id}"
  peer_vpc_id = "${var.peer_vpc_id}"
  auto_accept = "true"
  peer_owner_id = "${var.account_number}"

  tags {
    Name = "dev-stage-peering"
  }
}

resource "aws_route" "dev-stage-public-peering" {
  route_table_id = "${module.vpc.public_route_table_id}"
  destination_cidr_block = "${var.peer_vpc_cidr_block}"
  vpc_peering_connection_id = "${aws_vpc_peering_connection.dev-stage.id}"
}

resource "aws_route" "dev-stage-private-peering" {
  route_table_id = "${module.vpc.private_route_table_id}"
  destination_cidr_block = "${var.peer_vpc_cidr_block}"
  vpc_peering_connection_id = "${aws_vpc_peering_connection.dev-stage.id}"
}

env = "dev"
master_state_file = "${var.env}.tfstate"
cidr = "10.50.0.0/16"
road_warrior_vpn_cidr_block = "192.168.21.0/24"
road_warrior_vpn_instance_id = "i-f84a55d8"
public_ranges = "10.50.0.0/24,10.50.1.0/24,10.50.2.0/24,10.50.3.0/24"
private_ranges = "10.50.110.0/24,10.50.111.0/24,10.50.112.0/24,10.50.113.0/24"
peer_vpc_id = "vpc-1e45b27b"
peer_vpc_cidr_block = "10.50.0.0/16"

terragrunt {
  # Configure Terragrunt to use DynamoDB for locking
  lock = {
    backend = "dynamodb"
    config {
      state_file_id = "master_state"
    }
  }

  # Configure Terragrunt to automatically store tfstate files in S3
  remote_state = {
    backend = "s3"
    config {
      encrypt = "true"
      bucket = "${var.tf_s3_bucket}"
      key = "${var.env}.tfstate"
      region = "${var.region}"
    }
  }
}

data "terraform_remote_state" "env" {
  backend = "s3"

  config {
    bucket = "et-terraform"
    key    = "${var.env}.tfstate"
    region = "us-east-1"
  }
}

resource "aws_instance" "chef_instance" {
  count = "${var.instance_count}"

  provisioner "chef" {
    node_name       = "${var.env}-${var.name}${var.instance_count <= 1 ? "" : "-${count.index + 1}"}"
    environment     = "${var.env}"
    run_list        = "${var.run_list}"
    secret_key      = "${file("~/.chef/encrypted_data_bag_secret")}"
    server_url      = "https://api.chef.io/organizations/evertrue"
    user_name       = "${var.chef_user_name}"
    user_key        = "${file("${var.chef_client_key_file}")}"
    recreate_client = true
    version         = "${var.chef_version}"
    skip_install    = "${var.skip_install}"

    connection {
      user         = "ubuntu"
      private_key  = "${file("~/.ssh/${var.key_pair_id}.pem")}"
    }
  }

  ami                  = "${var.current_ami}"
  instance_type        = "${var.instance_type}"
  key_name             = "${var.key_pair_id}"
  subnet_id            = "${var.subnet_id}"
  iam_instance_profile = "${var.iam_instance_profile}"

  # root_block_device = {
  #   volume_size = "32"
  # }

  vpc_security_group_ids = ["${var.security_group_ids}"]

  tags {
    Name = "${var.env}-${var.name}${var.instance_count <= 1 ? "" : "-${count.index + 1}"}"
    Env  = "${var.env}"
    Operator = "${var.chef_user_name}"
    Job = "${var.job}"
    Type = "${var.type}"
    Datadog = "${var.datadog}"
  }
}

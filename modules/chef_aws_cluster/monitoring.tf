# # StatusCheckFailed >= 1 for 5 minutes
# resource "aws_cloudwatch_metric_alarm" "statuscheck" {
#   # count = "${length(aws_instance.chef_instance.*.id)}"
#   count = "${var.instance_count}"

#   alarm_name                = "${var.env}-${var.name}${var.instance_count <= 1 ? "" : "-${count.index + 1}"} Status Check Failed (any)"
#   comparison_operator       = "GreaterThanOrEqualToThreshold"
#   evaluation_periods        = "${var.statuscheck_evaluation_periods}"
#   metric_name               = "StatusCheckFailed"
#   namespace                 = "AWS/EC2"
#   period                    = "60"
#   statistic                 = "Maximum"
#   threshold                 = "1"
#   alarm_description         = "Managed by Terraform"
#   insufficient_data_actions = []
#   alarm_actions             = ["${var.alarm_action_arns}"]
#   ok_actions                = ["${var.ok_action_arns}"]

#   dimensions {
#     InstanceId = "${element(aws_instance.chef_instance.*.id, count.index)}"
#   }
# }

# # "${element(aws_subnet.public.*.id, count.index)}"

variable "env" {
  type = "string"
}

variable "instance_count" {
  default = 1
}

variable "instance_type" {
  type = "string"
}

variable "run_list" {
  type = "list"
}

variable "chef_user_name" {
  type = "string"
}

variable "chef_client_key_file" {
  type = "string"
}

variable "name" {
  type = "string"
}

variable "key_pair_id" {
  type = "string"
  default = "aws_dev"
}

variable "current_ami" {
  type = "string"
}

variable "security_group_ids" {
  type = "list"
  default = []
}

variable "subnet_id" {}

variable "job" {
  type = "string"
}

variable "type" {
  type = "string"
}

variable "datadog" {
  type = "string"
  default = ""
}

variable "index_start" {
  default = "0"
}

variable "iam_instance_profile" {
  default = ""
  type = "string"
}

variable "chef_version" {
  type = "string"
  default = "12.17.44"
}

variable "skip_install" {
  default = true
}

variable "alarm_action_arns" {
  default = ["arn:aws:sns:us-east-1:037590317780:OpsPagerDuty"]
}

variable "ok_action_arns" {
  default = ["arn:aws:sns:us-east-1:037590317780:OpsPagerDuty"]
}

variable "statuscheck_evaluation_periods" {
  default = "2"
}

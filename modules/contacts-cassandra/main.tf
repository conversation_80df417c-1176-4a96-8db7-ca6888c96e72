module "contacts-cassandra_1b" {
  source = "../../modules/chef_aws_cluster"

  name = "contacts-cass-1b"
  env = "${var.env}"
  instance_count = "${var.node_count >= 3 ? var.node_count / 3 : (var.node_count == 0 ? 0 : 1)}"
  instance_type = "${var.instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.run_list}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.contacts-cassandra.id}"
  ]

  job = "cassandra"
  type = "data_storage"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[1]}"
}

module "contacts-cassandra_1c" {
  source = "../../modules/chef_aws_cluster"

  name = "contacts-cass-1c"
  env = "${var.env}"
  instance_count = "${var.node_count >= 3 ? var.node_count / 3 : (var.node_count == 2 ? 1 : 0)}"
  instance_type = "${var.instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.run_list}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.contacts-cassandra.id}"
  ]

  job = "cassandra"
  type = "data_storage"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[2]}"
}

module "contacts-cassandra_1d" {
  source = "../../modules/chef_aws_cluster"

  name = "contacts-cass-1d"
  env = "${var.env}"
  instance_count = "${var.node_count >= 3 ? var.node_count / 3 : (var.node_count == 3 ? 1 : 0)}"
  instance_type = "${var.instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.run_list}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.contacts-cassandra.id}"
  ]

  job = "cassandra"
  type = "data_storage"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[3]}"
}

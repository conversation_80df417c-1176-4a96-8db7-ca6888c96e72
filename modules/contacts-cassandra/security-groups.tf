resource "aws_security_group" "search-contacts" {
  name = "${var.env}-contacts-search-v2"
  description = "${var.env}-contacts-search-v2"
  vpc_id = "${var.vpc_id}"

  tags {
    Env = "${var.env}"
    Name = "${var.env}-contacts-search-v2"
    Type = "data_storage"
  }
}

# self: tcp/22
resource "aws_security_group_rule" "search-contacts_self_22" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 22
  to_port = 22
  self = true
}

# storm-supervisor: tcp/9300
resource "aws_security_group_rule" "search-contacts_storm-supervisor_9300" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9300
  to_port = 9300
  source_security_group_id = "${var.storm-supervisor_sg_id}"
}

# storm-nimbus: tcp/9300
resource "aws_security_group_rule" "search-contacts_storm-nimbus_9300" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9300
  to_port = 9300
  source_security_group_id = "${var.storm-nimbus_sg_id}"
}

# contacts-cassandra: tcp/9300
resource "aws_security_group_rule" "search-contacts_contacts-cassandra_9300" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9300
  to_port = 9300
  source_security_group_id = "${var.contacts-cassandra_sg_id}"
}

# spark: tcp/9300
resource "aws_security_group_rule" "search-contacts_spark_9300" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9300
  to_port = 9300
  source_security_group_id = "${var.spark_sg_id}"
}

# mesos-slave: tcp/9300
resource "aws_security_group_rule" "search-contacts_mesos-slave_9300" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9300
  to_port = 9300
  source_security_group_id = "${var.mesos-slave_sg_id}"
}

# self: tcp/9300-9400
resource "aws_security_group_rule" "search-contacts_self_9300" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9300
  to_port = 9300
  self = true
}

# auth-api: tcp/9200-9300
resource "aws_security_group_rule" "search-contacts_auth-api_9200-9300" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9200
  to_port = 9300
  source_security_group_id = "${var.auth-api_sg_id}"
}

# contacts-cassandra: tcp/9200
resource "aws_security_group_rule" "search-contacts_contacts-cassandra_9200" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9200
  to_port = 9200
  source_security_group_id = "${var.contacts-cassandra_sg_id}"
}

# spark: tcp/9200
resource "aws_security_group_rule" "search-contacts_spark_9200" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9200
  to_port = 9200
  source_security_group_id = "${var.spark_sg_id}"
}

# mesos-slave: tcp/9200
resource "aws_security_group_rule" "search-contacts_mesos-slave_9200" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9200
  to_port = 9200
  source_security_group_id = "${var.mesos-slave_sg_id}"
}

# sodas-api: tcp/9200
resource "aws_security_group_rule" "search-contacts_sodas-api_9200" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9200
  to_port = 9200
  source_security_group_id = "${var.sodas-api_sg_id}"
}

# monitor: tcp/9200
resource "aws_security_group_rule" "search-contacts_monitor_9200" {
  security_group_id = "${aws_security_group.search-contacts.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9200
  to_port = 9200
  source_security_group_id = "${var.monitor_sg_id}"
}

# Egress Rule

resource "aws_security_group_rule" "search-contacts_egress_all" {
  security_group_id = "${aws_security_group.search-contacts.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

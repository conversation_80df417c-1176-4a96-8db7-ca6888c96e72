variable "chef_user_name" {}
variable "chef_client_key_file" {}
variable "data_node_count" {}
variable "env" {}
variable "key_pair_id" {}
variable "private_subnet_ids" { type = "list" }
variable "vpc_id" {}

# Security Group IDs
variable "auth-api_sg_id" {}
variable "contacts-cassandra_sg_id" {}
variable "default-limited_sg_id" {}
variable "mesos-slave_sg_id" {}
variable "monitor_sg_id" {}
variable "sodas-api_sg_id" {}
variable "spark_sg_id" {}
variable "storm-supervisor_sg_id" {}
variable "storm-nimbus_sg_id" {}

variable "datadog" {
  default = ""
}

variable "instance_type" {
  default = "c3.4xlarge"
}

variable "hvm_ami" {
  default = "ami-44e9752c"
}

variable "run_list" {
  type = "list"
  default = ["recipe[et_base]", "recipe[et_contacts_cassandra]", "recipe[et_contacts_mcrouter]"]
}

module "spark_1b" {
  source = "../../modules/chef_aws_cluster"

  name = "contacts-spark-1b"
  env = "${var.env}"
  instance_count = "${var.node_count >= 3 ? var.node_count / 3 : (var.node_count == 0 ? 0 : 1)}"
  instance_type = "${var.instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = ["recipe[et_base]", "recipe[et_contacts_spark]", "role[spark_master]"]
  skip_install = true

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.contacts-spark.id}",
    "${aws_security_group.contacts-spark-master.id}"
  ]

  job = "spark"
  type = "data_processing"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[1]}"
}

module "spark_1c" {
  source = "../../modules/chef_aws_cluster"

  name = "contacts-spark-1c"
  env = "${var.env}"
  instance_count = "${var.node_count >= 3 ? var.node_count / 3 : (var.node_count == 0 ? 0 : 1)}"
  instance_type = "${var.instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = ["recipe[et_base]", "recipe[et_contacts_spark]", "role[spark_master]"]
  skip_install = true

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.contacts-spark.id}",
    "${aws_security_group.contacts-spark-master.id}"
  ]


  job = "spark"
  type = "data_processing"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[2]}"
}

module "spark_1d" {
  source = "../../modules/chef_aws_cluster"

  name = "contacts-spark-1d"
  env = "${var.env}"
  instance_count = "${var.node_count >= 3 ? var.node_count / 3 : (var.node_count == 0 ? 0 : 1)}"
  instance_type = "${var.instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = ["recipe[et_base]", "recipe[et_contacts_spark]", "role[spark_master]"]
  skip_install = true

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.contacts-spark.id}",
    "${aws_security_group.contacts-spark-master.id}"
  ]

  job = "spark"
  type = "data_processing"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[3]}"
}

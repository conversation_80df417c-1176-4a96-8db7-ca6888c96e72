# Worker Group

resource "aws_security_group" "contacts-spark" {
  name = "${var.env}-spark"
  description = "Access control for ${var.env} Spark cluster"
  vpc_id = "${var.vpc_id}"

  tags {
    Env = "${var.env}"
    Name = "${var.env}-contacts-spark"
    Type = "data_processing"
  }
}

# master: tcp/32768-65535
resource "aws_security_group_rule" "contacts-spark_master_32768-65535" {
  security_group_id = "${aws_security_group.contacts-spark.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 32768
  to_port = 65535
  source_security_group_id = "${aws_security_group.contacts-spark-master.id}"
}

# self: tcp/32768-65535
resource "aws_security_group_rule" "contacts-spark_self_32768-65535" {
  security_group_id = "${aws_security_group.contacts-spark.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 32768
  to_port = 65535
  self = true
}

# monitor: tcp/8081
resource "aws_security_group_rule" "contacts-spark_monitor_8081" {
  security_group_id = "${aws_security_group.contacts-spark.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 8081
  to_port = 8081
  source_security_group_id = "${var.monitor_sg_id}"
}

# This is temporary until we move the spark nodes off of the cassandra nodes:

# contacts-spark: tcp/7077
resource "aws_security_group_rule" "contacts-spark_self_7077" {
  security_group_id = "${aws_security_group.contacts-spark.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 7077
  to_port = 7077
  self = true
}

# Egress Rule

resource "aws_security_group_rule" "contacts-spark_egress_all" {
  security_group_id = "${aws_security_group.contacts-spark.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

# Master Group

resource "aws_security_group" "contacts-spark-master" {
  name = "${var.env}-contacts-spark-master"
  description = "Contacts Spark Master (${var.env})"
  vpc_id = "${var.vpc_id}"

  tags {
    Env = "${var.env}"
    Name = "${var.env}-contacts-spark-master"
    Type = "data_processing"
  }
}

# contacts-spark: tcp/7077
resource "aws_security_group_rule" "contacts-spark-master_worker_7077" {
  security_group_id = "${aws_security_group.contacts-spark-master.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 7077
  to_port = 7077
  source_security_group_id = "${aws_security_group.contacts-spark.id}"
}

# monitor: tcp/8081
resource "aws_security_group_rule" "contacts-spark_monitor_8080" {
  security_group_id = "${aws_security_group.contacts-spark-master.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 8080
  to_port = 8080
  source_security_group_id = "${var.monitor_sg_id}"
}

# Egress Rule

resource "aws_security_group_rule" "contacts-spark-master_egress_all" {
  security_group_id = "${aws_security_group.contacts-spark-master.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

variable "chef_user_name" {}
variable "chef_client_key_file" {}
variable "node_count" {}
variable "env" {}
variable "key_pair_id" {}
variable "private_subnet_ids" { type = "list" }
variable "vpc_id" {}

# Security Group IDs
variable "auth-api_sg_id" {}
variable "contacts-cassandra_sg_id" {}
variable "default_sg_id" {}
variable "mesos-slave_sg_id" {}
variable "monitor_sg_id" {}
variable "sodas-api_sg_id" {}
variable "storm-supervisor_sg_id" {}

variable "run_list" {
  type = "list"
  default = ["recipe[et_base]", "recipe[et_contacts_spark]"]
}

variable "datadog" {
  default = ""
}

variable "instance_type" {
  default = "i3.xlarge"
}

variable "hvm_ami" {
  default = "ami-f8fc7fee"
}

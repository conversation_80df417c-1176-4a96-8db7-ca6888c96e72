module "lb_1b" {
  source = "../../modules/chef_aws_cluster"

  env = "${var.env}"
  instance_type = "c3.large"
  instance_count = "${var.instance_count >= 3 ? var.instance_count / 3 : (var.instance_count == 0 ? 0 : 1)}"

  name = "lb-1b"
  run_list = "${var.run_list}"
  iam_instance_profile = "nginx-lb"
  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.lb.id}"
  ]
  job = "loadbalancer"
  type = "infra"

  current_ami = "${var.current_ami}"
  subnet_id = "${var.private_subnet_ids[1]}"
  key_pair_id = "aws_dev"
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"
}

module "lb_1c" {
  source = "../../modules/chef_aws_cluster"

  env = "${var.env}"
  instance_type = "c3.large"
  instance_count = "${var.instance_count >= 3 ? var.instance_count / 3 : (var.instance_count <= 1 ? 0 : 1)}"

  name = "lb-1c"
  run_list = "${var.run_list}"
  iam_instance_profile = "nginx-lb"
  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.lb.id}"
  ]
  job = "loadbalancer"
  type = "infra"

  current_ami = "${var.current_ami}"
  subnet_id = "${var.private_subnet_ids[2]}"
  key_pair_id = "aws_dev"
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"
}

module "lb_1d" {
  source = "../../modules/chef_aws_cluster"

  env = "${var.env}"
  instance_type = "c3.large"
  instance_count = "${var.instance_count >= 3 ? var.instance_count / 3 : (var.instance_count <= 2 ? 0 : 1)}"

  name = "lb-1d"
  run_list = "${var.run_list}"
  iam_instance_profile = "nginx-lb"
  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.lb.id}"
  ]
  job = "loadbalancer"
  type = "infra"

  current_ami = "${var.current_ami}"
  subnet_id = "${var.private_subnet_ids[3]}"
  key_pair_id = "aws_dev"
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"
}

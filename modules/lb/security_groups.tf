resource "aws_security_group" "lb" {
  name = "${var.env}-lb"
  description = "${var.sg_description}"
  vpc_id = "${var.vpc_id}"

  tags {
    Env = "${var.env}"
  }
}

resource "aws_security_group_rule" "lb_singularity_8882-8900" {
  security_group_id = "${aws_security_group.lb.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 8882
  to_port = 8900
  source_security_group_id = "${var.singularity_sg_id}"
}

resource "aws_security_group_rule" "web-services_lb_8080" {
  security_group_id = "${var.web-services_sg_id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 8080
  to_port = 8080
  source_security_group_id = "${aws_security_group.lb.id}"
}

# Egress Rule

resource "aws_security_group_rule" "lb_egress_all" {
  security_group_id = "${aws_security_group.lb.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

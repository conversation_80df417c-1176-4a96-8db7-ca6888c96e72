variable "env" {}
variable "instance_count" {
  default = 1
}
variable "chef_user_name" {}
variable "chef_client_key_file" {}

variable "run_list" {
  type = "list"
  default = ["recipe[et_base]", "recipe[et_api_lb]"]
}

variable "current_ami" {
  default = "ami-a1f951ca"
}

variable "sg_description" {}

# variable "api-elb-internal_sg_id" {}
# variable "public-lb_sg_id" {}
variable "singularity_sg_id" {}
variable "default_sg_id" {}
variable "web-services_sg_id" {}
variable "vpc_id" {}
variable "private_subnet_ids" {
  type = "list"
}

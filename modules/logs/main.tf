module "logs_1a" {
  source = "../../modules/chef_aws_cluster"

  name = "logs-1a-1"
  env = "${var.env}"
  instance_count = "${var.data_node_count >= 3 ? var.data_node_count / 3 : (var.data_node_count == 0 ? 0 : 1)}"
  instance_type = "r3.large"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.run_list}"
  skip_install = false

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.logs.id}"
  ]

  job = "logs"
  type = "infra"
  datadog = "${var.datadog}"
  iam_instance_profile = "logs-host"
  alarm_action_arns = "${var.alarm_action_arns}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[0]}"
}

module "logs_1b" {
  source = "../../modules/chef_aws_cluster"

  name = "logs-1b-1"
  env = "${var.env}"
  instance_count = "${var.data_node_count >= 3 ? var.data_node_count / 3 : (var.data_node_count == 2 ? 1 : 0)}"
  instance_type = "r3.large"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.run_list}"
  skip_install = false

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.logs.id}"
  ]

  job = "logs"
  type = "infra"
  datadog = "${var.datadog}"
  iam_instance_profile = "logs-host"
  alarm_action_arns = "${var.alarm_action_arns}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[1]}"
}

module "logs_1d" {
  source = "../../modules/chef_aws_cluster"

  name = "logs-1d-1"
  env = "${var.env}"
  instance_count = "${var.data_node_count >= 3 ? var.data_node_count / 3 : (var.data_node_count == 3 ? 1 : 0)}"
  instance_type = "r3.large"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.run_list}"
  skip_install = false

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.logs.id}"
  ]

  job = "logs"
  type = "infra"
  datadog = "${var.datadog}"
  iam_instance_profile = "logs-host"
  alarm_action_arns = "${var.alarm_action_arns}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[3]}"
}

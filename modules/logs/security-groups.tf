resource "aws_security_group" "logs" {
  name = "${var.env}-logs"
  description = "${var.long_env_icase} logs group"
  vpc_id = "${var.vpc_id}"

  tags {
    Env = "${var.env}"
    Name = "${var.env}-logs"
    Type = "infra"
  }
}

# monitor: tcp/514
resource "aws_security_group_rule" "logs_default_514" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 514
  to_port = 514
  source_security_group_id = "${var.default_sg_id}"
}

# self: tcp/22
resource "aws_security_group_rule" "logs_self_22" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 22
  to_port = 22
  self = true
}

# internal-lb: tcp/5601
resource "aws_security_group_rule" "logs_internal-lb_5601" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 5601
  to_port = 5601
  source_security_group_id = "${var.internal-lb_sg_id}"
}

# api-lb: tcp/5601
resource "aws_security_group_rule" "logs_api-lb_5601" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 5601
  to_port = 5601
  source_security_group_id = "${var.api-lb_sg_id}"
}

# self: tcp/9300
resource "aws_security_group_rule" "logs_self_9300" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9300
  to_port = 9300
  self = true
}

# logs-lb: tcp/5043-5044
resource "aws_security_group_rule" "logs_logs-lb_5043-5044" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 5043
  to_port = 5044
  source_security_group_id = "${aws_security_group.logs-lb.id}"
}

# default-limited: tcp/5043-5044
resource "aws_security_group_rule" "logs_default-limited_5043-5044" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 5043
  to_port = 5044
  source_security_group_id = "${var.default-limited_sg_id}"
}

# default: tcp/5043-5044
resource "aws_security_group_rule" "logs_default_5043-5044" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 5043
  to_port = 5044
  source_security_group_id = "${var.default_sg_id}"
}

# self: tcp/9200
resource "aws_security_group_rule" "logs_self_9200" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9200
  to_port = 9200
  self = true
}

# monitor: tcp/9200
resource "aws_security_group_rule" "logs_monitor_9200" {
  security_group_id = "${aws_security_group.logs.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 9200
  to_port = 9200
  source_security_group_id = "${var.monitor_sg_id}"
}

# Egress Rule
resource "aws_security_group_rule" "logs_egress_all" {
  security_group_id = "${aws_security_group.logs.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

#
# logs-lb group
#

resource "aws_security_group" "logs-lb" {
  name = "${var.env}-logs-lb"
  description = "${var.env_icase} logs ELB group"
  vpc_id = "${var.vpc_id}"

  tags {
    Env = "${var.env}"
    Name = "${var.env}-logs-lb"
    Type = "infra"
  }
}

# default-limited: tcp/5043-5044
resource "aws_security_group_rule" "logs-lb_default-limited_5043-5044" {
  security_group_id = "${aws_security_group.logs-lb.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 5043
  to_port = 5044
  source_security_group_id = "${var.default-limited_sg_id}"
}

# default: tcp/5043-5044
resource "aws_security_group_rule" "logs-lb_default_5043-5044" {
  security_group_id = "${aws_security_group.logs-lb.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 5043
  to_port = 5044
  source_security_group_id = "${var.default_sg_id}"
}

# Egress Rule
resource "aws_security_group_rule" "logs-lb_egress_all" {
  security_group_id = "${aws_security_group.logs-lb.id}"
  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

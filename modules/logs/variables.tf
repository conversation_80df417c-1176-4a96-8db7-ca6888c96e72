variable "chef_user_name" {}
variable "chef_client_key_file" {}
variable "data_node_count" {}
variable "env" {}
variable "env_icase" {}
variable "long_env_icase" {}
variable "key_pair_id" {}
variable "private_subnet_ids" { type = "list" }
variable "vpc_id" {}

# Security Group IDs
variable "default-limited_sg_id" {}
variable "default_sg_id" {}
variable "monitor_sg_id" {}
variable "api-lb_sg_id" {}
variable "internal-lb_sg_id" {}

variable "run_list" {
  type = "list"
  default = ["recipe[et_base]", "recipe[logserver]","role[es_master]"]
}

variable "datadog" {
  default = ""
}

variable "hvm_ami" {
  default = "ami-ca5003dd"
}

variable "alarm_action_arns" {
  default = ["arn:aws:sns:us-east-1:037590317780:OpsPagerDuty"]
}

# variable "pv_ami" {
#   default = "ami-fa8201ec"
# }

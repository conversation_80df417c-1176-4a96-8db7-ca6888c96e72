module "mesos-agent_1b" {
  source = "../../modules/chef_aws_cluster"

  name = "mesos-agent-1b-1"
  env = "${var.env}"
  instance_count = "${var.agent_node_count >= 3 ? var.agent_node_count / 3 : (var.agent_node_count == 0 ? 0 : 1)}"
  instance_type = "${var.mesos-agent_instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = ["${var.mesos-agent_run_list}"]
  iam_instance_profile = "${var.iam_instance_profile}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.mesos-slave.id}"
  ]

  job = "mesos-agent"
  type = "infra"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[1]}"
}

module "mesos-agent_1c" {
  source = "../../modules/chef_aws_cluster"

  name = "mesos-agent-1c-1"
  env = "${var.env}"
  instance_count = "${var.agent_node_count >= 3 ? var.agent_node_count / 3 : (var.agent_node_count == 2 ? 1 : 0)}"
  instance_type = "${var.mesos-agent_instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = ["${var.mesos-agent_run_list}"]
  iam_instance_profile = "${var.iam_instance_profile}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.mesos-slave.id}"
  ]

  job = "mesos-agent"
  type = "infra"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[2]}"
}

module "mesos-agent_1d" {
  source = "../../modules/chef_aws_cluster"

  name = "mesos-agent-1d-1"
  env = "${var.env}"
  instance_count = "${var.agent_node_count >= 3 ? var.agent_node_count / 3 : (var.agent_node_count == 3 ? 1 : 0)}"
  instance_type = "${var.mesos-agent_instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = ["${var.mesos-agent_run_list}"]
  iam_instance_profile = "${var.iam_instance_profile}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.mesos-slave.id}"
  ]

  job = "mesos-agent"
  type = "infra"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[3]}"
}

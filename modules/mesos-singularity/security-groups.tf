resource "aws_security_group" "mesos-slave" {
  name = "${var.env}-mesos-slave"
  description = "${var.env_icase} Mesos Slave Group"
  vpc_id = "${var.vpc_id}"

  tags {
    Env = "${var.env}"
    Name = "${var.env}-mesos-slave"
    Type = "infra"
  }
}

resource "aws_security_group_rule" "mesos-slave_default_all" {
  security_group_id = "${aws_security_group.mesos-slave.id}"
  type = "ingress"
  protocol = "-1"
  from_port = 31000
  to_port = 32000
  source_security_group_id = "${var.default_sg_id}"
}

# self: tcp/22
# resource "aws_security_group_rule" "search-contacts_self_22" {
#   security_group_id = "${aws_security_group.search-contacts.id}"
#   type = "ingress"
#   protocol = "tcp"
#   from_port = 22
#   to_port = 22
#   self = true
# }

# Egress Rule

resource "aws_security_group_rule" "mesos-slave_egress_all" {
  security_group_id = "${aws_security_group.mesos-slave.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

resource "aws_security_group" "singularity" {
  name = "${var.env}-singularity"
  description = "${var.env_icase} Singularity Group"
  vpc_id = "${var.vpc_id}"

  tags {
    Env = "${var.env}"
    Name = "${var.env}-singularity"
    Type = "infra"
  }
}

# Egress Rule

resource "aws_security_group_rule" "singularity_egress_all" {
  security_group_id = "${aws_security_group.singularity.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

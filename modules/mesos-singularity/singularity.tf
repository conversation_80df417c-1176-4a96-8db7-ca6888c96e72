module "singularity_1b" {
  source = "../../modules/chef_aws_cluster"

  name = "singularity-1b-1"
  env = "${var.env}"
  instance_count = 1
  instance_type = "${var.singularity_instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.singularity_run_list}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.mesos-slave.id}",
    "${aws_security_group.singularity.id}"
  ]

  job = "singularity"
  type = "infra"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[1]}"
}

module "singularity_1c" {
  source = "../../modules/chef_aws_cluster"

  name = "singularity-1c-1"
  env = "${var.env}"
  instance_count = 1
  instance_type = "${var.singularity_instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.singularity_run_list}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.mesos-slave.id}",
    "${aws_security_group.singularity.id}"
  ]


  job = "singularity"
  type = "infra"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[2]}"
}

module "singularity_1d" {
  source = "../../modules/chef_aws_cluster"

  name = "singularity-1d-1"
  env = "${var.env}"
  instance_count = 1
  instance_type = "${var.singularity_instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.singularity_run_list}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${aws_security_group.mesos-slave.id}",
    "${aws_security_group.singularity.id}"
  ]

  job = "singularity"
  type = "infra"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[3]}"
}

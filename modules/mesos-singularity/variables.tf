variable "chef_user_name" {}
variable "chef_client_key_file" {}
variable "agent_node_count" {}
variable "env" {}
variable "env_icase" {}
variable "key_pair_id" {}
variable "private_subnet_ids" { type = "list" }
variable "vpc_id" {}

# Security Group IDs
variable "default_sg_id" {}
variable "monitor_sg_id" {}

variable "singularity_run_list" {
  type = "list"
  default = ["recipe[et_base]", "recipe[et_singularity]"]
}

variable "mesos-agent_run_list" {
  type = "list"
  default = ["recipe[et_base]", "recipe[et_mesos_slave]"]
}

variable "datadog" {
  default = ""
}

variable "singularity_instance_type" {
  default = "c3.large"
}

variable "mesos-agent_instance_type" {
  default = "c3.4xlarge"
}

variable "hvm_ami" {
  default = "ami-08128d1f"
}

variable "pv_ami" {
  default = "ami-fa8201ec"
}

variable "iam_instance_profile" {
  type = "string"
}

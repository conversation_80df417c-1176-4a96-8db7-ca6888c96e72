module "data_1b" {
  source = "../../modules/chef_aws_cluster"

  name = "search-v2-contacts-1b"
  env = "${var.env}"
  instance_count = "${var.data_node_count >= 3 ? var.data_node_count / 3 : (var.data_node_count == 0 ? 0 : 1)}"
  instance_type = "${var.data_instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = ["recipe[et_base]", "recipe[et_contacts_search]"]

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default-limited_sg_id}",
    "${aws_security_group.search-contacts.id}"
  ]

  job = "elasticsearch-data"
  type = "data_storage"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[1]}"
}

module "data_1c" {
  source = "../../modules/chef_aws_cluster"

  name = "search-v2-contacts-1c"
  env = "${var.env}"
  instance_count = "${var.data_node_count >= 3 ? var.data_node_count / 3 : (var.data_node_count == 2 ? 1 : 0)}"
  instance_type = "${var.data_instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = ["recipe[et_base]", "recipe[et_contacts_search]"]

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default-limited_sg_id}",
    "${aws_security_group.search-contacts.id}"
  ]

  job = "elasticsearch-data"
  type = "data_storage"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[2]}"
}

module "data_1d" {
  source = "../../modules/chef_aws_cluster"

  name = "search-v2-contacts-1d"
  env = "${var.env}"
  instance_count = "${var.data_node_count >= 3 ? var.data_node_count / 3 : (var.data_node_count == 3 ? 1 : 0)}"
  instance_type = "${var.data_instance_type}"
  current_ami = "${var.hvm_ami}"
  run_list = ["recipe[et_base]", "recipe[et_contacts_search]"]

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default-limited_sg_id}",
    "${aws_security_group.search-contacts.id}"
  ]

  job = "elasticsearch-data"
  type = "data_storage"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[3]}"
}

module "master_1b" {
  source = "../../modules/chef_aws_cluster"

  name = "searchmaster-v2-contacts-1b"
  env = "${var.env}"
  instance_count = 1
  instance_type = "${var.master_instance_type}"
  current_ami = "${var.pv_ami}"
  run_list = "${var.master_run_list}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default-limited_sg_id}",
    "${aws_security_group.search-contacts.id}"
  ]

  job = "elasticsearch-master"
  type = "data_storage"
  datadog = "${var.datadog}"
  alarm_action_arns = [
    "arn:aws:sns:us-east-1:037590317780:OpsPagerDuty",
    "arn:aws:swf:us-east-1:037590317780:action/actions/AWS_EC2.InstanceId.Reboot/1.0"
  ]
  statuscheck_evaluation_periods = "5"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[1]}"
}

module "master_1c" {
  source = "../../modules/chef_aws_cluster"

  name = "searchmaster-v2-contacts-1c"
  env = "${var.env}"
  instance_count = 1
  instance_type = "${var.master_instance_type}"
  current_ami = "${var.pv_ami}"
  run_list = "${var.master_run_list}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default-limited_sg_id}",
    "${aws_security_group.search-contacts.id}"
  ]


  job = "elasticsearch-master"
  type = "data_storage"
  datadog = "${var.datadog}"
  alarm_action_arns = [
    "arn:aws:sns:us-east-1:037590317780:OpsPagerDuty",
    "arn:aws:swf:us-east-1:037590317780:action/actions/AWS_EC2.InstanceId.Reboot/1.0"
  ]
  statuscheck_evaluation_periods = "5"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[2]}"
}

module "master_1d" {
  source = "../../modules/chef_aws_cluster"

  name = "searchmaster-v2-contacts-1d"
  env = "${var.env}"
  instance_count = 1
  instance_type = "${var.master_instance_type}"
  current_ami = "${var.pv_ami}"
  run_list = "${var.master_run_list}"

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default-limited_sg_id}",
    "${aws_security_group.search-contacts.id}"
  ]

  job = "elasticsearch-master"
  type = "data_storage"
  datadog = "${var.datadog}"
  alarm_action_arns = [
    "arn:aws:sns:us-east-1:037590317780:OpsPagerDuty",
    "arn:aws:swf:us-east-1:037590317780:action/actions/AWS_EC2.InstanceId.Reboot/1.0"
  ]
  statuscheck_evaluation_periods = "5"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[3]}"
}

module "sodas-worker_1b" {
  source = "../../modules/chef_aws_cluster"

  name = "api-sodas-worker-1b"
  env = "${var.env}"
  instance_count = "${var.node_count >= 3 ? var.node_count / 3 : (var.node_count == 0 ? 0 : 1)}"
  instance_type = "m3.large"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.run_list}"
  skip_install = false

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${var.web-services_sg_id}",
    "${var.sodas-api_sg_id}"
  ]

  job = "api-worker"
  type = "data_processing"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[1]}"
}

module "sodas-worker_1c" {
  source = "../../modules/chef_aws_cluster"

  name = "api-sodas-worker-1c"
  env = "${var.env}"
  instance_count = "${var.node_count >= 3 ? var.node_count / 3 : (var.node_count == 2 ? 1 : 0)}"
  instance_type = "m3.large"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.run_list}"
  skip_install = false

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${var.web-services_sg_id}",
    "${var.sodas-api_sg_id}"
  ]

  job = "api-worker"
  type = "data_processing"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[2]}"
}

module "sodas-worker_1d" {
  source = "../../modules/chef_aws_cluster"

  name = "api-sodas-worker-1d"
  env = "${var.env}"
  instance_count = "${var.node_count >= 3 ? var.node_count / 3 : (var.node_count == 3 ? 1 : 0)}"
  instance_type = "m3.large"
  current_ami = "${var.hvm_ami}"
  run_list = "${var.run_list}"
  skip_install = false

  key_pair_id = "${var.key_pair_id}"

  security_group_ids = [
    "${var.default_sg_id}",
    "${var.web-services_sg_id}",
    "${var.sodas-api_sg_id}"
  ]

  job = "api-worker"
  type = "data_processing"
  datadog = "${var.datadog}"

  chef_user_name       = "${var.chef_user_name}" # From Env vars
  chef_client_key_file = "${var.chef_client_key_file}" # From Env vars
  subnet_id            = "${var.private_subnet_ids[3]}"
}

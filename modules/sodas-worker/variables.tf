variable "chef_user_name" {}
variable "chef_client_key_file" {}
variable "node_count" {}
variable "env" {}
variable "env_icase" {}
variable "key_pair_id" {}
variable "private_subnet_ids" { type = "list" }
variable "vpc_id" {}

# Security Group IDs
variable "default_sg_id" {}
variable "sodas-api_sg_id" {}
variable "web-services_sg_id" {}

variable "run_list" {
  type = "list"
  default = ["recipe[et_base]", "recipe[et_sodas_api_worker]"]
}

variable "datadog" {
  default = ""
}

variable "hvm_ami" {
  default = "ami-37fb8520"
}

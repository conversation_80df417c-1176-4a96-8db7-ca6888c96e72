# file name terraform/modules/aws_vpc/vpc.tf
# first create the VPC.
# Prefix resources with var.name so we can have many environments trivially

resource "aws_vpc" "mod" {
  cidr_block = "${var.cidr}"
  enable_dns_hostnames = "${var.enable_dns_hostnames}"
  enable_dns_support = "${var.enable_dns_support}"
  tags { 
    Name = "${var.env}-vpc"
    Env = "${var.env}"
  }
}

resource "aws_internet_gateway" "mod" {
  vpc_id = "${aws_vpc.mod.id}"
  tags { 
    Name = "${var.env}-igw"
    Env = "${var.env}"
  }
}

# for each in the list of availability zones, create the public subnet 
# and private subnet for that list index,
# then create an EIP and attach a nat_gateway for each one.  and an aws route
# table should be created for each private subnet, and add the correct nat_gw

resource "aws_subnet" "private" {
  vpc_id = "${aws_vpc.mod.id}"
  cidr_block = "${element(split(",", var.private_ranges), count.index)}"
  availability_zone = "${element(var.azs, count.index)}"
  count = "${length(compact(split(",", var.private_ranges)))}"
  tags { 
    Name = "${var.env}-private-${element(var.azs, count.index)}"
    Env = "${var.env}"
  }
}

resource "aws_subnet" "public" {
  vpc_id = "${aws_vpc.mod.id}"
  cidr_block = "${element(split(",", var.public_ranges), count.index)}"
  availability_zone = "${element(var.azs, count.index)}"
  count = "${length(compact(split(",", var.public_ranges)))}"
  tags { 
    Name = "${var.env}-public-${element(var.azs, count.index)}"
    Env = "${var.env}"
  }
}

# refactor to take all the route {} sections out of routing tables, 
# and turn them into associated aws_route resources
# so we can add vpc peering routes from specific environments.
resource "aws_route_table" "public" {
  vpc_id = "${aws_vpc.mod.id}"
  tags { 
    Name = "Direct Internet Access"
    Env = "${var.env}"
  }
}

# add a public gateway to each public route table
resource "aws_route" "public_gateway_route" {
  route_table_id = "${aws_route_table.public.id}"
  depends_on = ["aws_route_table.public"]
  destination_cidr_block = "0.0.0.0/0"
  gateway_id = "${aws_internet_gateway.mod.id}"
}

resource "aws_route_table_association" "public" {
  count = "${length(compact(split(",", var.public_ranges)))}"
  subnet_id = "${element(aws_subnet.public.*.id, count.index)}"
  route_table_id = "${aws_route_table.public.id}"
}



#
#
# Single point of failure things that should be fixed (The HA versions are below)
#
#

# add a nat gateway to each private subnet's route table
resource "aws_route" "private_nat_gateway_route" {
  route_table_id = "${aws_route_table.private.id}"
  destination_cidr_block = "0.0.0.0/0"
  depends_on = ["aws_route_table.private"]
  nat_gateway_id = "${aws_nat_gateway.nat_gw.id}"
}

resource "aws_route" "road_warrior_private_vpn_route" {
  count = "${length(var.road_warrior_vpn_cidr_blocks)}"
  destination_cidr_block = "${element(var.road_warrior_vpn_cidr_blocks, count.index)}"
  route_table_id = "${aws_route_table.private.id}"
  instance_id = "${var.road_warrior_vpn_instance_id}"
}

resource "aws_route" "road_warrior_public_vpn_route" {
  count = "${length(var.road_warrior_vpn_cidr_blocks)}"
  destination_cidr_block = "${element(var.road_warrior_vpn_cidr_blocks, count.index)}"
  route_table_id = "${aws_route_table.public.id}"
  instance_id = "${var.road_warrior_vpn_instance_id}"
}

resource "aws_main_route_table_association" "private" {
  vpc_id = "${aws_vpc.mod.id}"
  route_table_id = "${aws_route_table.private.id}"
}

resource "aws_nat_gateway" "nat_gw" {
  allocation_id = "${aws_eip.nat_eip.id}"
  subnet_id = "${var.nat_subnet_id}"
  depends_on = ["aws_internet_gateway.mod"]
}

resource "aws_eip" "nat_eip" {
  vpc = true
}

resource "aws_route_table" "private" {
  vpc_id = "${aws_vpc.mod.id}"
  propagating_vgws = ["${var.propagating_vgws}"]
  tags {
    Name = "NAT Internet Access"
    Env = "${var.env}"
  }
}

#
#
# These things are all commented out because they require us to have NATs/Routes/VPN Gateways in all
# AZs. This is something we should have but we don't yet.
#
#


# resource "aws_eip" "nat_eip" {
#   count = "${length(split(",", var.public_ranges))}"
#   vpc = true
# }

# resource "aws_nat_gateway" "nat_gw" {
#   count = "${length(split(",", var.public_ranges))}"
#   allocation_id = "${element(aws_eip.nat_eip.*.id, count.index)}"
#   subnet_id = "${element(aws_subnet.public.*.id, count.index)}"
#   depends_on = ["aws_internet_gateway.mod"]
# }

#
#
# These things are 

# for each of the private ranges, create a "private" route table.
# Uncomment when we're ready to do HA
# resource "aws_route_table" "private" {
#   vpc_id = "${aws_vpc.mod.id}"
#   propagating_vgws = ["${aws_vpn_gateway.office.*.id}"]
#   count = "${length(compact(split(",", var.private_ranges)))}"
#   tags {
#     Name = "${var.env}_private_subnet_route_table_${count.index}"
#     Env = "${var.env}"
#   }
# }

# add a nat gateway to each private subnet's route table
# resource "aws_route" "private_nat_gateway_route" {
#   count = "${length(compact(split(",", var.private_ranges)))}"
#   route_table_id = "${element(aws_route_table.private.*.id, count.index)}"
#   destination_cidr_block = "0.0.0.0/0"
#   depends_on = ["aws_route_table.private"]
#   nat_gateway_id = "${element(aws_nat_gateway.nat_gw.*.id, count.index)}"
# }

# gonna need a custom route association for each range too
# resource "aws_route_table_association" "private" {
#   count = "${length(compact(split(",", var.private_ranges)))}"
#   subnet_id = "${element(aws_subnet.private.*.id, count.index)}"
#   route_table_id = "${element(aws_route_table.private.*.id, count.index)}"
# }

output "vpc_id" {
  value = "${aws_vpc.mod.id}"
}

output "public_route_table_id" {
  value = "${aws_route_table.public.id}"
}

output "private_route_table_id" {
  value = "${aws_route_table.private.id}"
}

output "private_subnet_ids" {
  value = ["${aws_subnet.private.*.id}"]
}

output "public_subnet_ids" {
  value = ["${aws_subnet.public.*.id}"]
}

output "default_sg_id" {
  value = "${aws_security_group.default.id}"
}

output "default-limited_sg_id" {
  value = "${aws_security_group.default-limited.id}"
}

output "monitor_sg_id" {
  value = "${aws_security_group.monitor.id}"
}

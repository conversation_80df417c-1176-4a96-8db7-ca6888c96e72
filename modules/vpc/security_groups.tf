# SECURITY GROUPS

resource "aws_security_group" "default" {
  name = "${var.env}-default"
  description = "Default SSH, DNS, NAT, Bastion access"
  vpc_id = "${aws_vpc.mod.id}"

  tags {
    Env = "${var.env}"
  }
}

# This group mainly exists so that we can exclude Elasticsearch from the office IP block
# allow rule
resource "aws_security_group" "default-limited" {
  name = "${var.env}-default-limited"
  description = "${var.env_icase} Default Limited (Like ${var.env}-default without blanket permission${var.pluralize_default-limited})"
  vpc_id = "${aws_vpc.mod.id}"

  tags {
    Env = "${var.env}"
  }
}

resource "aws_security_group" "monitor" {
  name = "${var.env}-monitor"
  description = "${var.env_icase} Monitoring Group"
  vpc_id = "${aws_vpc.mod.id}"

  tags {
    Env = "${var.env}"
  }
}

resource "aws_security_group" "vpn" {
  name = "${var.env}-vpn"
  description = "${var.env_icase} VPN Group"
  vpc_id = "${aws_vpc.mod.id}"

  tags {
    Env = "${var.env}"
  }
}

# SECURITY GROUP EGRESS RULES (GROUP: ${env}-default)

resource "aws_security_group_rule" "default_egress_all" {
  security_group_id = "${aws_security_group.default.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

# SECURITY GROUP INGRESS RULES (GROUP: ${env}-default)

resource "aws_security_group_rule" "default_monitor_8080_tcp" {
  security_group_id = "${aws_security_group.default.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 8080
  to_port = 8080
  source_security_group_id = "${aws_security_group.monitor.id}"
}

resource "aws_security_group_rule" "default_monitor_snmp" {
  security_group_id = "${aws_security_group.default.id}"

  type = "ingress"
  from_port = 161
  to_port = 161
  protocol = "udp"
  source_security_group_id = "${aws_security_group.monitor.id}"
}

resource "aws_security_group_rule" "default_vpn_access" {
  security_group_id = "${aws_security_group.default.id}"
  type = "ingress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = [
    "${concat(list(var.office_cidr_block),var.road_warrior_vpn_cidr_blocks)}"
  ]
}

resource "aws_security_group_rule" "default_monitor_ssh" {
  security_group_id = "${aws_security_group.default.id}"

  type = "ingress"
  from_port = 22
  to_port = 22
  protocol = "tcp"
  source_security_group_id = "${aws_security_group.monitor.id}"
}

resource "aws_security_group_rule" "default_self_8301_tcp" {
  security_group_id = "${aws_security_group.default.id}"

  type = "ingress"
  self = true
  protocol = "tcp"
  from_port = 8301
  to_port = 8301
}

resource "aws_security_group_rule" "default_limited_8301_tcp" {
  security_group_id = "${aws_security_group.default.id}"

  type = "ingress"
  protocol = "tcp"
  from_port = 8301
  to_port = 8301
  source_security_group_id = "${aws_security_group.default-limited.id}"
}

resource "aws_security_group_rule" "default_self_8301_udp" {
  security_group_id = "${aws_security_group.default.id}"

  type = "ingress"
  self = true
  protocol = "udp"
  from_port = 8301
  to_port = 8301
}

resource "aws_security_group_rule" "default_limited_8301_udp" {
  security_group_id = "${aws_security_group.default.id}"

  type = "ingress"
  protocol = "udp"
  from_port = 8301
  to_port = 8301
  source_security_group_id = "${aws_security_group.default-limited.id}"
}

resource "aws_security_group_rule" "default_monitor_icmp" {
  security_group_id = "${aws_security_group.default.id}"

  type = "ingress"
  from_port = "-1"
  to_port = "-1"
  protocol = "icmp"
  source_security_group_id = "${aws_security_group.monitor.id}"
}

# SECURITY GROUP EGRESS RULES (GROUP: ${env}-default-limited)

resource "aws_security_group_rule" "default-limited_egress_all" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

# SECURITY GROUP INGRESS RULES (GROUP: ${env}-default-limited)

resource "aws_security_group_rule" "default-limited_monitor_8080_tcp" {
  security_group_id = "${aws_security_group.default-limited.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 8080
  to_port = 8080
  source_security_group_id = "${aws_security_group.monitor.id}"
}

resource "aws_security_group_rule" "default-limited_monitor_snmp" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  from_port = 161
  to_port = 161
  protocol = "udp"
  source_security_group_id = "${aws_security_group.monitor.id}"
}

resource "aws_security_group_rule" "default-limited_monitor_ssh" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  from_port = 22
  to_port = 22
  protocol = "tcp"
  source_security_group_id = "${aws_security_group.monitor.id}"
}

resource "aws_security_group_rule" "default-limited_vpn-client_ssh" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  from_port = 22
  to_port = 22
  protocol = "tcp"
  cidr_blocks = "${concat(list(var.office_cidr_block),var.road_warrior_vpn_cidr_blocks)}"
}

resource "aws_security_group_rule" "default-limited_self_8301_udp" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  self = true
  protocol = "udp"
  from_port = 8301
  to_port = 8301
}

resource "aws_security_group_rule" "default-limited_default_8301_udp" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  protocol = "udp"
  from_port = 8301
  to_port = 8301
  source_security_group_id = "${aws_security_group.default.id}"
}

resource "aws_security_group_rule" "default-limited_vpn-clients_9200" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  protocol = "tcp"
  from_port = 9200
  to_port = 9200
  cidr_blocks = "${var.road_warrior_vpn_cidr_blocks}"
}

resource "aws_security_group_rule" "default-limited_vpn-clients_9300" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  protocol = "tcp"
  from_port = 9300
  to_port = 9300
  cidr_blocks = "${var.road_warrior_vpn_cidr_blocks}"
}

resource "aws_security_group_rule" "default-limited_self_8301_tcp" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  self = true
  protocol = "tcp"
  from_port = 8301
  to_port = 8301
}

resource "aws_security_group_rule" "default-limited_default_8301_tcp" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  protocol = "tcp"
  from_port = 8301
  to_port = 8301
  source_security_group_id = "${aws_security_group.default.id}"
}

resource "aws_security_group_rule" "default-limited_monitor_icmp" {
  security_group_id = "${aws_security_group.default-limited.id}"

  type = "ingress"
  from_port = "-1"
  to_port = "-1"
  protocol = "icmp"
  source_security_group_id = "${aws_security_group.monitor.id}"
}

# SECURITY GROUP EGRESS RULES (GROUP: ${env}-monitor)

resource "aws_security_group_rule" "monitor_egress_all" {
  security_group_id = "${aws_security_group.monitor.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

# SECURITY GROUP INGRESS RULES (GROUP: ${env}-monitor)

resource "aws_security_group_rule" "monitor_default-limited_50000" {
  security_group_id = "${aws_security_group.monitor.id}"

  type = "ingress"
  protocol = "tcp"
  from_port = 50000
  to_port = 50000
  source_security_group_id = "${aws_security_group.default-limited.id}"
}

resource "aws_security_group_rule" "monitor_default_50000" {
  security_group_id = "${aws_security_group.monitor.id}"

  type = "ingress"
  protocol = "tcp"
  from_port = 50000
  to_port = 50000
  source_security_group_id = "${aws_security_group.default.id}"
}

resource "aws_security_group_rule" "monitor_internal-lb_7767" {
  security_group_id = "${aws_security_group.monitor.id}"

  type = "ingress"
  protocol = "tcp"
  from_port = 7767
  to_port = 7767
  source_security_group_id = "${var.internal_lb_sg_id}"
}

# SECURITY GROUP EGRESS RULES (GROUP: ${env}-vpn)

resource "aws_security_group_rule" "vpn_egress_all" {
  security_group_id = "${aws_security_group.vpn.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

# SECURITY GROUP INGRESS RULES (GROUP: ${env}-vpn)

resource "aws_security_group_rule" "vpn_global_1194_udp" {
  security_group_id = "${aws_security_group.vpn.id}"

  type = "ingress"
  protocol = "udp"
  from_port = 1194
  to_port = 1194
  cidr_blocks = ["0.0.0.0/0"]
}

resource "aws_security_group_rule" "vpn_office_ssh" {
  security_group_id = "${aws_security_group.vpn.id}"

  type = "ingress"
  protocol = "tcp"
  from_port = 22
  to_port = 22
  cidr_blocks = ["${var.office_external_netblocks}"]
}



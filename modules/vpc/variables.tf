variable "cidr" {}
variable "env" {}
variable "env_icase" {}
variable "enable_dns_hostnames" {}
variable "enable_dns_support" {}

variable "public_ranges" { }
variable "private_ranges" { }
variable "azs" {
  type = "list"
}

variable "road_warrior_vpn_cidr_blocks" {
  type = "list"
}
variable "road_warrior_vpn_instance_id" {}
variable "office_external_netblocks" {
  type = "list"
}

variable "internal_lb_sg_id" {}
variable "nat_subnet_id" {}
variable "propagating_vgws" {
  type = "list"
  default = []
}
variable "office_cidr_block" {}
variable "pluralize_default-limited" {
  type = "string"
  default = ""
}

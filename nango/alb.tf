locals {
  loadbalancers = {
    nango = {
      name                  = "nango"
      load_balancer_type    = "application"
      vpc_id                = module.vpc["prod"].vpc_id
      subnets               = module.vpc["prod"].public_subnets
      create_security_group = false
      security_groups       = [module.security_group["prod-lb"].security_group_id, module.security_group["prod-self"].security_group_id]
      access_logs = {
        enabled = true
        bucket  = "et-nango-prod"
      }
      target_groups = [
        {
          name                 = "nango-server"
          backend_protocol     = "HTTP"
          backend_port         = 3003
          deregistration_delay = 10
          target_type          = "ip"
          health_check = {
            enabled             = true
            interval            = 10
            path                = "/health"
            port                = "traffic-port"
            healthy_threshold   = 2
            unhealthy_threshold = 2
            timeout             = 5
            protocol            = "HTTP"
            matcher             = "200-399"
          }
        }
      ]
      http_tcp_listeners = [
        {
          port        = 80
          protocol    = "HTTP"
          action_type = "redirect"
          redirect = {
            port        = "443"
            protocol    = "HTTPS"
            status_code = "HTTP_301"
          }
        }
      ]
      https_listeners = [
        {
          port     = 443
          protocol = "HTTPS"
          #          certificate_arn    = data.aws_acm_certificate.pledgemine_com.arn
          certificate_arn    = "arn:aws:acm:us-east-1:905418190808:certificate/e230a3ec-c267-4ec0-9094-8b9937a84bd2"
          ssl_policy         = "ELBSecurityPolicy-TLS13-1-2-2021-06"
          target_group_index = 0
          tags               = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
        }
      ]
      idle_timeout = 600
      tags         = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
    }
    nango-jobs = {
      name                  = "nango-jobs"
      load_balancer_type    = "application"
      vpc_id                = module.vpc["prod"].vpc_id
      subnets               = module.vpc["prod"].private_subnets
      create_security_group = false
      security_groups       = [module.security_group["prod-self"].security_group_id]
      internal              = true
      access_logs = {
        enabled = true
        bucket  = "et-nango-prod"
      }
      target_groups = [
        {
          name                 = "nango-jobs"
          backend_protocol     = "HTTP"
          backend_port         = 3005
          deregistration_delay = 10
          target_type          = "ip"
          health_check = {
            enabled             = true
            interval            = 5
            path                = "/health"
            port                = "traffic-port"
            healthy_threshold   = 2
            unhealthy_threshold = 2
            timeout             = 2
            protocol            = "HTTP"
            matcher             = "200-399"
          }
        }
      ]
      http_tcp_listeners = [
        {
          port               = 80
          protocol           = "HTTP"
          target_group_index = 0
          # action_type = "redirect"
          # redirect = {
          #   port        = "443"
          #   protocol    = "HTTPS"
          #   status_code = "HTTP_301"
          # }
        }
      ]
      # https_listeners = [
      #   {
      #     port     = 443
      #     protocol = "HTTPS"
      #     #          certificate_arn    = data.aws_acm_certificate.pledgemine_com.arn
      #     certificate_arn    = "arn:aws:acm:us-east-1:905418190808:certificate/e230a3ec-c267-4ec0-9094-8b9937a84bd2"
      #     target_group_index = 0
      #     tags               = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
      #   }
      # ]
      idle_timeout = 600
      tags         = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
    }
    nango-persist = {
      name                  = "nango-persist"
      load_balancer_type    = "application"
      vpc_id                = module.vpc["prod"].vpc_id
      subnets               = module.vpc["prod"].private_subnets
      create_security_group = false
      security_groups       = [module.security_group["prod-self"].security_group_id]
      internal              = true
      access_logs = {
        enabled = true
        bucket  = "et-nango-prod"
      }
      target_groups = [
        {
          name                 = "nango-persist"
          backend_protocol     = "HTTP"
          backend_port         = 3007
          deregistration_delay = 10
          target_type          = "ip"
          health_check = {
            enabled             = true
            interval            = 5
            path                = "/health"
            port                = "traffic-port"
            healthy_threshold   = 2
            unhealthy_threshold = 2
            timeout             = 2
            protocol            = "HTTP"
            matcher             = "200-399"
          }
        }
      ]
      http_tcp_listeners = [
        {
          port               = 80
          protocol           = "HTTP"
          target_group_index = 0
          # action_type = "redirect"
          # redirect = {
          #   port        = "443"
          #   protocol    = "HTTPS"
          #   status_code = "HTTP_301"
          # }
        }
      ]
      # https_listeners = [
      #   {
      #     port     = 443
      #     protocol = "HTTPS"
      #     #          certificate_arn    = data.aws_acm_certificate.pledgemine_com.arn
      #     certificate_arn    = "arn:aws:acm:us-east-1:905418190808:certificate/e230a3ec-c267-4ec0-9094-8b9937a84bd2"
      #     target_group_index = 0
      #     tags               = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
      #   }
      # ]
      idle_timeout = 600
      tags         = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
    }
    nango-runner = {
      name                  = "nango-runner"
      load_balancer_type    = "application"
      vpc_id                = module.vpc["prod"].vpc_id
      subnets               = module.vpc["prod"].private_subnets
      create_security_group = false
      security_groups       = [module.security_group["prod-self"].security_group_id]
      internal              = true
      access_logs = {
        enabled = true
        bucket  = "et-nango-prod"
      }
      target_groups = [
        {
          name                 = "nango-runner"
          backend_protocol     = "HTTP"
          backend_port         = 80
          deregistration_delay = 10
          target_type          = "ip"
          health_check = {
            enabled             = true
            interval            = 130
            path                = "/health"
            port                = "traffic-port"
            healthy_threshold   = 2
            unhealthy_threshold = 3
            timeout             = 120
            protocol            = "HTTP"
            matcher             = "200-399"
          }
        }
      ]
      http_tcp_listeners = [
        {
          port               = 80
          protocol           = "HTTP"
          target_group_index = 0
          # action_type = "redirect"
          # redirect = {
          #   port        = "443"
          #   protocol    = "HTTPS"
          #   status_code = "HTTP_301"
          # }
        }
      ]
      # https_listeners = [
      #   {
      #     port     = 443
      #     protocol = "HTTPS"
      #     #          certificate_arn    = data.aws_acm_certificate.pledgemine_com.arn
      #     certificate_arn    = "arn:aws:acm:us-east-1:905418190808:certificate/e230a3ec-c267-4ec0-9094-8b9937a84bd2"
      #     target_group_index = 0
      #     tags               = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
      #   }
      # ]
      idle_timeout = 600
      tags         = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
    }
    nango-orchestrator = {
      name                  = "nango-orchestrator"
      load_balancer_type    = "application"
      vpc_id                = module.vpc["prod"].vpc_id
      subnets               = module.vpc["prod"].private_subnets
      create_security_group = false
      security_groups       = [module.security_group["prod-self"].security_group_id]
      internal              = true
      access_logs = {
        enabled = true
        bucket  = "et-nango-prod"
      }
      target_groups = [
        {
          name                 = "nango-orchestrator"
          backend_protocol     = "HTTP"
          backend_port         = 3008
          deregistration_delay = 10
          target_type          = "ip"
          health_check = {
            enabled             = true
            interval            = 5
            path                = "/health"
            port                = "traffic-port"
            healthy_threshold   = 2
            unhealthy_threshold = 2
            timeout             = 2
            protocol            = "HTTP"
            matcher             = "200-399"
          }
        }
      ]
      http_tcp_listeners = [
        {
          port               = 80
          protocol           = "HTTP"
          target_group_index = 0
          # action_type = "redirect"
          # redirect = {
          #   port        = "443"
          #   protocol    = "HTTPS"
          #   status_code = "HTTP_301"
          # }
        }
      ]
      # https_listeners = [
      #   {
      #     port     = 443
      #     protocol = "HTTPS"
      #     #          certificate_arn    = data.aws_acm_certificate.pledgemine_com.arn
      #     certificate_arn    = "arn:aws:acm:us-east-1:905418190808:certificate/e230a3ec-c267-4ec0-9094-8b9937a84bd2"
      #     target_group_index = 0
      #     tags               = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
      #   }
      # ]
      idle_timeout = 600
      tags         = "${merge(var.commonTagsGlobal, { Ticket = "ET-19973", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "20230720" })}"
    }
  }
}

module "alb" {
  source  = "terraform-aws-modules/alb/aws"
  version = "~> 8.7.0"

  for_each = local.loadbalancers

  name                  = each.key
  load_balancer_type    = try(each.value.load_balancer_type, "application")
  vpc_id                = try(each.value.vpc_id, "")
  subnets               = try(each.value.subnets, [])
  internal              = try(each.value.internal, false)
  create_security_group = try(each.value.create_security_group, false)
  security_groups       = try(each.value.security_groups, [])
  access_logs           = try(each.value.access_logs, {})
  target_groups         = try(each.value.target_groups, [])
  https_listeners       = try(each.value.https_listeners, [])
  http_tcp_listeners    = try(each.value.http_tcp_listeners, [])
  idle_timeout          = try(each.value.idle_timeout, 60)
  tags                  = try(each.value.tags, {})
}

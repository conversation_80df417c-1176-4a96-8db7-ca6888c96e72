locals {
  iam-users = {
    # steadfast_prod = {
    #   create_iam_access_key = true
    #   policy_arns           = [module.iam_policy_from_data_source["ProdAppS3Access"].arn]
    # }
  }
}

module "iam_user" {
  source  = "terraform-aws-modules/iam/aws//modules/iam-user"
  version = "~> 5.0"

  for_each = local.iam-users

  name                    = each.key
  create_login_profile    = false
  create_access_keys      = try(each.value.create_iam_access_key, false)
  attach_policy_arns      = try(each.value.policy_arns, [])
}

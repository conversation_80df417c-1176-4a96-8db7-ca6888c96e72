terraform {
  backend "s3" {
    bucket = "et-terraform"
    key = "prod.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "master_state" {
  backend = "s3"
  config {
    bucket = "${var.tf_s3_bucket}"
    region = "${var.region}"
    key = "${var.master_state_file}"
  }
}

data "terraform_remote_state" "stage" {
  backend = "s3"
  config {
    bucket = "${var.tf_s3_bucket}"
    region = "${var.region}"
    key = "stage.tfstate"
  }
}

module "vpc" {
  source = "../modules/vpc"

  cidr = "${var.cidr}"
  enable_dns_support = "${var.enable_dns_support}"
  enable_dns_hostnames = "${var.enable_dns_hostnames}"
  public_ranges = "${var.public_ranges}"
  private_ranges = "${var.private_ranges}"
  office_cidr_block = "${var.office_cidr_block}"
  office_external_netblocks = "${var.office_external_netblocks}"
  road_warrior_vpn_cidr_blocks = "${var.road_warrior_vpn_cidr_blocks}"
  road_warrior_vpn_instance_id = "${var.road_warrior_vpn_instance_id}"
  nat_subnet_id = "${module.vpc.public_subnet_ids[2]}"
  internal_lb_sg_id = "sg-82e80ae4"
  azs = "${var.azs}"
  env = "${var.env}"
  env_icase = "${var.env_icase}"
}

# resource "aws_vpc_peering_connection" "stage-prod" {
#   vpc_id = "${module.vpc.vpc_id}"
#   peer_vpc_id = "${var.peer_vpc_id}"
#   auto_accept = "true"
#   peer_owner_id = "${var.account_number}"

#   tags {
#     Name = "stage-prod-peering"
#   }
# }

# PEERING

resource "aws_route" "stage-prod-public-peering" {
  route_table_id = "${module.vpc.public_route_table_id}"
  destination_cidr_block = "${var.peer_vpc_cidr_block}"
  vpc_peering_connection_id = "${data.terraform_remote_state.stage.stage-prod_peering_connection_id}"
}

resource "aws_route" "stage-prod-private-peering" {
  route_table_id = "${module.vpc.private_route_table_id}"
  destination_cidr_block = "${var.peer_vpc_cidr_block}"
  vpc_peering_connection_id = "${data.terraform_remote_state.stage.stage-prod_peering_connection_id}"
}

# VPN

# resource "aws_vpn_gateway" "office" {
#   vpc_id = "${module.vpc.vpc_id}"
#   availability_zone = "us-east-1c"

#   tags {
#     Name = "${var.env}-office-vpg"
#     Env = "${var.env}"
#   }
# }

# Office VPN tunnel (r-rtb-3e7f965b3062066622)
resource "aws_route" "office_vpn_private_tunnel" {
  route_table_id = "${module.vpc.private_route_table_id}"
  destination_cidr_block = "${var.office_cidr_block}"
  # depends_on = ["aws_route_table.private"]
  instance_id = "i-dfe4278d"
}

resource "aws_route" "office_vpn_public_tunnel" {
  route_table_id = "${module.vpc.public_route_table_id}"
  destination_cidr_block = "${var.office_cidr_block}"
  # depends_on = ["aws_route_table.public"]
  instance_id = "i-dfe4278d"
}

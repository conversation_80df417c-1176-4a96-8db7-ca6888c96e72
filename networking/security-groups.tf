resource "aws_security_group" "vpn-tunnel" {
  name = "${var.env}-vpn-tunnel"
  description = "${var.env_icase} VPN Tunnel Group"
  vpc_id = "${module.vpc.vpc_id}"

  tags {
    Env = "${var.env}"
  }
}

# SECURITY GROUP EGRESS RULES (GROUP: ${env}-vpn-tunnel)

resource "aws_security_group_rule" "vpn-tunnel_egress_all" {
  security_group_id = "${aws_security_group.vpn-tunnel.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

# SECURITY GROUP INGRESS RULES (GROUP: ${env}-vpn-tunnel)

resource "aws_security_group_rule" "vpn-tunnel_office_ssh" {
  security_group_id = "${aws_security_group.vpn-tunnel.id}"

  type = "ingress"
  protocol = "tcp"
  from_port = 22
  to_port = 22
  cidr_blocks = ["${var.office_external_netblocks}"]
}

resource "aws_security_group_rule" "vpn-tunnel_office_4500_udp" {
  security_group_id = "${aws_security_group.vpn-tunnel.id}"

  type = "ingress"
  protocol = "udp"
  from_port = 4500
  to_port = 4500
  cidr_blocks = ["${var.office_external_netblocks}"]
}

resource "aws_security_group_rule" "vpn-tunnel_office_500_udp" {
  security_group_id = "${aws_security_group.vpn-tunnel.id}"

  type = "ingress"
  protocol = "udp"
  from_port = 500
  to_port = 500
  cidr_blocks = ["${var.office_external_netblocks}"]
}

# Security Group: web-services

resource "aws_security_group" "web-services" {
  name = "${var.env}-web-services"
  description = "Web Services Group (${var.env})"
  vpc_id = "${module.vpc.vpc_id}"

  tags {
    Env = "${var.env}"
    Name = "${var.env}-web-services"
  }
}

# internal-lb: tcp/8080
resource "aws_security_group_rule" "web-services_internal-lb_8080" {
  security_group_id = "${aws_security_group.web-services.id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 8080
  to_port = 8080
  source_security_group_id = "${var.internal-lb_sg_id}"
}

# lb: tcp/8080
# This group is defined in the lb module for inheritence reasons

# SECURITY GROUP EGRESS RULE
resource "aws_security_group_rule" "web-services_egress_all" {
  security_group_id = "${aws_security_group.web-services.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

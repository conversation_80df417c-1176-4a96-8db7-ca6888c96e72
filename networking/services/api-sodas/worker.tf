terraform {
  backend "s3" {
    bucket = "et-terraform"
    key = "prod/services/api-sodas.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "env" {
  backend = "s3"
  config {
    bucket = "et-terraform"
    region = "us-east-1"
    key = "prod.tfstate"
  }
}

module "sodas-worker" {
  source = "../../../modules/sodas-worker"

  env = "${var.env}"
  env_icase = "${var.env_icase}"

  node_count           = 2
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"
  key_pair_id          = "${var.key_pair_id}"

  default_sg_id         = "${data.terraform_remote_state.env.default_sg_id}"
  web-services_sg_id    = "${data.terraform_remote_state.env.web-services_sg_id}"
  sodas-api_sg_id       = "sg-f8a1af81"

  private_subnet_ids = "${data.terraform_remote_state.env.private_subnet_ids}"
  vpc_id             = "vpc-9318d5f8"
}

terraform {
  backend "s3" {
    bucket = "et-terraform"
    key = "prod/services/contacts-spark.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "env" {
  backend = "s3"
  config {
    bucket = "et-terraform"
    region = "us-east-1"
    key = "prod.tfstate"
  }
}


module "contacts-spark" {
  source = "../../../modules/contacts-spark"

  env = "${var.env}"

  node_count           = 6
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"
  key_pair_id          = "${var.key_pair_id}"
  datadog              = "monitored"

  default_sg_id         = "${data.terraform_remote_state.env.default_sg_id}"
  mesos-slave_sg_id        = "${data.terraform_remote_state.env.mesos-slave_sg_id}"
  monitor_sg_id            = "${data.terraform_remote_state.env.monitor_sg_id}"
  auth-api_sg_id           = "${data.terraform_remote_state.env.auth-api_sg_id}"
  sodas-api_sg_id          = "${data.terraform_remote_state.env.sodas-api_sg_id}"
  # contacts-cassandra_sg_id = "${data.terraform_remote_state.env.contacts-cassandra_sg_id}"
  contacts-cassandra_sg_id = "sg-e7a92683"
  storm-supervisor_sg_id   = "${data.terraform_remote_state.env.storm-supervisor_sg_id}"

  private_subnet_ids = "${data.terraform_remote_state.env.private_subnet_ids}"
  vpc_id             = "${data.terraform_remote_state.env.vpc_id}"
}

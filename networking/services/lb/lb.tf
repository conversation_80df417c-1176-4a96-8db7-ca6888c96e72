terraform {
  backend "s3" {
    bucket = "et-terraform"
    key = "prod/services/lb.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "env" {
  backend = "s3"
  config {
    bucket = "et-terraform"
    region = "us-east-1"
    key = "${var.env}.tfstate"
  }
}

# Instance Configuration

module "lb" {
  source = "../../../modules/lb"

  env = "${var.env}"
  instance_count = 2
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"

  singularity_sg_id = "${var.singularity_sg_id}"
  default_sg_id = "${data.terraform_remote_state.env.default_sg_id}"
  web-services_sg_id = "${data.terraform_remote_state.env.web-services_sg_id}"

  sg_description = "${var.sg_description}"

  private_subnet_ids = "${data.terraform_remote_state.env.private_subnet_ids}"
  vpc_id = "${data.terraform_remote_state.env.vpc_id}"
}

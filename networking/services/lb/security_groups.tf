resource "aws_security_group_rule" "lb_api-elb_80" {
  security_group_id = "${module.lb.lb_sg_id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 80
  to_port = 80
  source_security_group_id = "${var.api-elb_sg_id}"
}

resource "aws_security_group_rule" "lb_api-elb_8069" {
  security_group_id = "${module.lb.lb_sg_id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 8069
  to_port = 8069
  source_security_group_id = "${var.api-elb_sg_id}"
}

resource "aws_security_group_rule" "lb_api-elb_8443" {
  security_group_id = "${module.lb.lb_sg_id}"
  type = "ingress"
  protocol = "tcp"
  from_port = 8443
  to_port = 8443
  source_security_group_id = "${var.api-elb_sg_id}"
}

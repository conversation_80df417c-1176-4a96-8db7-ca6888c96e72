terraform {
  backend "s3" {
    bucket = "et-terraform"
    key = "prod/services/logs.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "env" {
  backend = "s3"
  config {
    bucket = "et-terraform"
    region = "us-east-1"
    key = "prod.tfstate"
  }
}


module "logs" {
  source = "../../../modules/logs"

  env = "${var.env}"
  env_icase = "${var.env_icase}"
  long_env_icase = "${var.long_env_icase}"

  data_node_count      = 3
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"
  key_pair_id          = "${var.key_pair_id}"

  monitor_sg_id         = "${data.terraform_remote_state.env.monitor_sg_id}"
  default_sg_id         = "${data.terraform_remote_state.env.default_sg_id}"
  api-lb_sg_id          = "${data.terraform_remote_state.env.api-lb_sg_id}"
  internal-lb_sg_id     = "${data.terraform_remote_state.env.internal-lb_sg_id}"
  default-limited_sg_id = "${data.terraform_remote_state.env.default-limited_sg_id}"

  private_subnet_ids = "${data.terraform_remote_state.env.private_subnet_ids}"
  vpc_id             = "vpc-9318d5f8"
}

# Data block to pull all the EC2 instances
data "aws_instances" "all" {}

# Data source to fetch details for each EC2 instance
data "aws_instance" "each" {
  count = length(data.aws_instances.all.ids)
  instance_id = data.aws_instances.all.ids[count.index]
}

# CloudWatch metric alarm for each EC2 instance's CPU utilization
resource "aws_cloudwatch_metric_alarm" "ec2_cpu_utilization_alarms" {
  count               = length(data.aws_instances.all.ids)
  alarm_name          = "${lookup(data.aws_instance.each[count.index].tags, "Name", data.aws_instances.all.ids[count.index])}-cpu-utilization-alarm"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "15" # Alarm if CPU utilization is maintained for 15 minutes
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "60"
  statistic           = "Average"
  threshold           = "85" # Adjust to your requirements
  alarm_description   = "This metric checks CPU utilization for EC2 instances and triggers if CPU is greater than 85 for 15 minutes"
  alarm_actions       = [var.sns_topic_arn]

  dimensions = {
    InstanceId = data.aws_instances.all.ids[count.index]
  }
}

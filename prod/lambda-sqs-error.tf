# Reference the existing SNS topic
data "aws_sns_topic" "existing_sns" {
  name = "${var.account_name}-${data.aws_caller_identity.current.account_id}-monitoring-alarm"
}

resource "aws_cloudwatch_metric_alarm" "sqs_oldest_message_alarm" {
  for_each = toset(var.sqs_queues)

  alarm_name          = "SQS-ApproximateAgeOfOldestMessage-${each.value}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "ApproximateAgeOfOldestMessage"
  namespace           = "AWS/SQS"
  period              = "300"
  statistic           = "Maximum"
  threshold           = 5000
  alarm_description   = "This alarm will trigger when the oldest message in the SQS queue is older than 5000 seconds."
  alarm_actions     = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    QueueName = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}

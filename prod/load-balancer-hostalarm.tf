# Fetch the ALB details
data "aws_lb" "prod_public" {
  name = var.application_load_balancer
}

# Fetch the first listener of the ALB
data "aws_lb_listener" "prod_public_listener" {
  load_balancer_arn = data.aws_lb.prod_public.arn
  port              = 443
}

# Extract the target group ARN from the listener's default action
locals {
  target_group_arn = data.aws_lb_listener.prod_public_listener.default_action[0].target_group_arn
}

# Fetch the target group details using the extracted ARN
data "aws_lb_target_group" "prod_public_tg" {
  arn = local.target_group_arn
}


# CloudWatch Alarms for Classic Load Balancers
resource "aws_cloudwatch_metric_alarm" "classic_lb_alarms" {
  count               = length(var.load_balancers)
  alarm_name          = "${var.load_balancers[count.index]}-unhealthy-host"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "5"
  metric_name         = "Unhealthy Hosts"
  namespace           = "AWS/ELB"
  period              = "60"
  statistic           = "SampleCount"
  threshold           = "1"
  alarm_description   = "This metric checks unhealthy host count for classic LBs"
  alarm_actions       = [var.sns_topic_arn]
  
  dimensions = {
    LoadBalancerName = var.load_balancers[count.index]
  }
}

# CloudWatch Alarms for Application Load Balancer's target group
resource "aws_cloudwatch_metric_alarm" "alb_target_group_alarm" {
  alarm_name          = "${data.aws_lb_target_group.prod_public_tg.name}-unhealthy-alert"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "5"
  metric_name         = "UnHealthyHostCount"
  namespace           = "AWS/ApplicationELB"
  period              = "60"
  statistic           = "SampleCount"
  threshold           = "1"
  alarm_description   = "This metric checks unhealthy target count for ALB target group"
  alarm_actions       = [var.sns_topic_arn]
  
  dimensions = {
    TargetGroup  = data.aws_lb_target_group.prod_public_tg.name
    LoadBalancer = var.application_load_balancer
  }
}

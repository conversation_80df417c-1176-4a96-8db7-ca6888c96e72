#locals {
#  read_capacity_thresholds = {
#    "add table name" = 100000  # 100k
#  }
#  default_threshold = 50
#}

resource "aws_cloudwatch_metric_alarm" "dynamodb_consumed_read_capacity" {
  for_each = toset(var.dynamodb_tables)
  
  alarm_name          = "${each.value}-DynamoDB-ConsumedReadCapacityUnits"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "2" # 10 minutes / 5 minute periods
  metric_name         = "ConsumedReadCapacityUnits"
  namespace           = "AWS/DynamoDB"
  period              = "300" # 5 minutes in seconds
  statistic           = "Sum"
  threshold           = 50
  alarm_description   = "This alarm will trigger when ConsumedReadCapacityUnits exceeds 50 units for 10 minutes"
  #threshold           = lookup(local.read_capacity_thresholds, each.value, local.default_threshold)
  #alarm_description   = "This alarm will trigger when ConsumedReadCapacityUnits exceeds ${lookup(local.read_capacity_thresholds, each.value, local.default_threshold)} units for 10 minutes"
  alarm_actions       = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    TableName = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}

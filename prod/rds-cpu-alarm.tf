# Fetch all RDS instances in the AWS account
data "aws_db_instances" "all" {}

# Create a CloudWatch alarm for each RDS instance
resource "aws_cloudwatch_metric_alarm" "rds_cpu_utilization" {
  for_each = toset(data.aws_db_instances.all.instance_identifiers)

  alarm_name          = "${each.value}-RDS-CPUUtilization"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "1"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = "300" # 5 minutes in seconds
  statistic           = "Average"
  threshold           = var.cpu_utilization_threshold
  alarm_description   = "This alarm will trigger when CPU Utilization equals or greater than ${var.cpu_utilization_threshold}% for 5 minutes"
  alarm_actions       = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    DBInstanceIdentifier = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}
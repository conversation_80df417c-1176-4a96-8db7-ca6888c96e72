# Fetch details of each individual RDS instance.
data "aws_db_instance" "individual" {
  for_each               = toset(data.aws_db_instances.all.instance_identifiers)
  db_instance_identifier = each.value
}

resource "aws_cloudwatch_metric_alarm" "rds_free_storage" {
  for_each = toset(data.aws_db_instances.all.instance_identifiers)

  alarm_name          = "${each.value}-RDS-FreeStorageSpace"
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = "1"

  # Conditionally set the metric_name based on the engine of the RDS instance.
  metric_name = data.aws_db_instance.individual[each.value].engine == "aurora-mysql" ? "FreeLocalStorage" : "FreeStorageSpace"

  namespace         = "AWS/RDS"
  period            = "300" # 5 minutes in seconds
  statistic         = "Average"
  threshold         = 2 * 1073741824 # 2GB in bytes
  alarm_description = "This alarm will trigger when free storage space drops below 5GB for 5 minutes"
  alarm_actions     = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    DBInstanceIdentifier = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}
# Define the VPCs
data "aws_vpc" "stage" {
  id = "vpc-1e45b27b"
}

data "aws_vpc" "prod" {
  id = "vpc-9318d5f8"
}

# Create the private hosted zone for db.stage-us-east-1.consul
resource "aws_route53_zone" "stage_us_east_1_consul" {
  name = "stage-us-east-1.consul"
  vpc {
    vpc_id = data.aws_vpc.stage.id
  }
  vpc {
    vpc_id = data.aws_vpc.prod.id
  }
  comment = "Private hosted zone for stage-us-east-1.consul"
}

# Create the private hosted zone for db.prod-us-east-1.consul
resource "aws_route53_zone" "prod_us_east_1_consul" {
  name = "prod-us-east-1.consul"
  vpc {
    vpc_id = data.aws_vpc.stage.id
  }
  vpc {
    vpc_id = data.aws_vpc.prod.id
  }
  comment = "Private hosted zone for prod-us-east-1.consul"
}

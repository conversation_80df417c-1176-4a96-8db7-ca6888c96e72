resource "aws_customer_gateway" "expedient_gateway" {
  bgp_asn    = 65000  # Default BGP ASN
  ip_address = "*************"
  type       = "ipsec.1"  # Default type for IPSec VPN
  
  tags = {
    Name = "expedient-gateway"
  }
}


# Create the VPN Connection
resource "aws_vpn_connection" "expedient_to_prod" {
  customer_gateway_id = aws_customer_gateway.expedient_gateway.id
  vpn_gateway_id     = "vgw-bd15f6d4"
  type               = "ipsec.1"
  static_routes_only = false
  local_ipv4_network_cidr  = "************/32"
  remote_ipv4_network_cidr = "10.0.0.0/16"

  tunnel1_phase1_encryption_algorithms = ["AES128", "AES256", "AES256-GCM-16"]
  tunnel1_phase2_encryption_algorithms = ["AES128", "AES256", "AES256-GCM-16"]
  tunnel1_phase1_integrity_algorithms  = ["SHA1", "SHA2-256", "SHA2-384"]
  tunnel1_phase2_integrity_algorithms  = ["SHA1", "SHA2-256", "SHA2-384"]
  tunnel1_phase1_dh_group_numbers     = [2, 14, 19, 20, 24]
  tunnel1_phase2_dh_group_numbers     = [2, 14, 19, 20, 24]
  tunnel1_ike_versions                = ["ikev1", "ikev2"]
  tunnel1_phase1_lifetime_seconds     = 28800
  tunnel1_phase2_lifetime_seconds     = 3600
  
  tunnel1_log_options {
    cloudwatch_log_options {
      log_enabled       = true
      log_group_arn    = aws_cloudwatch_log_group.vpn_logs.arn
      log_output_format = "json"
    }
  }

  tunnel2_phase1_encryption_algorithms = ["AES128", "AES256", "AES256-GCM-16"]
  tunnel2_phase2_encryption_algorithms = ["AES128", "AES256", "AES256-GCM-16"]
  tunnel2_phase1_integrity_algorithms  = ["SHA1", "SHA2-256", "SHA2-384"]
  tunnel2_phase2_integrity_algorithms  = ["SHA1", "SHA2-256", "SHA2-384"]
  tunnel2_phase1_dh_group_numbers     = [2, 14, 19, 20, 24]
  tunnel2_phase2_dh_group_numbers     = [2, 14, 19, 20, 24]
  tunnel2_ike_versions                = ["ikev1", "ikev2"]
  tunnel2_phase1_lifetime_seconds     = 28800
  tunnel2_phase2_lifetime_seconds     = 3600

  tunnel2_log_options {
    cloudwatch_log_options {
      log_enabled       = true
      log_group_arn    = aws_cloudwatch_log_group.vpn_logs.arn
      log_output_format = "json"
    }
  }

  tags = {
    Name = "expedient-to-prod"
  }
}



# CloudWatch Log Group
resource "aws_cloudwatch_log_group" "vpn_logs" {
  name              = "/aws/site-to-site/vpn-logs"
  retention_in_days = 30
}

# IAM Role
resource "aws_iam_role" "vpn_logging_role" {
  name = "vpn-logging-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "logs.amazonaws.com"
        }
      }
    ]
  })
}

# IAM Policy
resource "aws_iam_policy" "vpn_logging_policy" {
  name = "vpn-logging-policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogDelivery",
          "logs:GetLogDelivery",
          "logs:UpdateLogDelivery",
          "logs:DeleteLogDelivery",
          "logs:ListLogDeliveries",
          "logs:PutLogEvents",
          "logs:CreateLogStream"
        ]
        Resource = ["*"]
        Effect = "Allow"
        Sid    = "S2SVPNLogging"
      },
      {
        Sid = "S2SVPNLoggingCWL"
        Action = [
          "logs:PutResourcePolicy",
          "logs:DescribeResourcePolicies",
          "logs:DescribeLogGroups"
        ]
        Resource = ["*"]
        Effect = "Allow"
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "vpn_logging_attachment" {
  policy_arn = aws_iam_policy.vpn_logging_policy.arn
  role       = aws_iam_role.vpn_logging_role.name
}

#locals {
#  write_capacity_thresholds = {
#    "importer-contact-cache" = 60000  # 60k
#  }
#  write_threshold = 200
#}

resource "aws_cloudwatch_metric_alarm" "dynamodb_consumed_write_capacity" {
  for_each = toset(var.dynamodb_tables)
  
  alarm_name          = "${each.value}-DynamoDB-ConsumedWriteCapacityUnits"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = "2" # 10 minutes / 5 minute periods
  metric_name         = "ConsumedWriteCapacityUnits"
  namespace           = "AWS/DynamoDB"
  period              = "900" # 15 minutes in seconds
  statistic           = "Sum"
  threshold           = 200
  alarm_description   = "This alarm will trigger when ConsumedReadCapacityUnits exceeds 200 units for 15 minutes"
  #threshold           = lookup(local.write_capacity_thresholds, each.value, local.write_threshold)
  #alarm_description   = "This alarm will trigger when ConsumedWriteCapacityUnits exceeds ${lookup(local.write_capacity_thresholds, each.value, local.write_threshold)} units for 15 minutes"
  alarm_actions       = [aws_sns_topic.rds_alarm.arn]

  dimensions = {
    TableName = each.value
  }

  tags = {
    "ManagedBy" = "Terraform"
    "Owner"     = "Michael D"
  }
}

#########ET-Legacy-subnets########

variable "shared_infra_prod_subnets" {
  description = "List of subnet IDs for the shared-infra-stage environment"
  type        = list(string)
  default     = [
    "subnet-421bd629",  // Private Subnet (AZ4)
    "subnet-5e1bd635",  // Private Subnet (AZ6)
    "subnet-681bd603",  // Public Subnet (AZ1)
    "subnet-7a1bd611",  // Public Subnet (AZ2)
  ]
}


variable "shared_infra_stage_subnets" {
  description = "List of subnet IDs for the shared-infra-stage environment"
  type        = list(string)
  default     = [
    "subnet-34e9de72",  // Private Subnet (AZ2)
    "subnet-09b2b87d",  // Private Subnet (AZ4)
    "subnet-af92da87",  // Public Subnet (AZ2)
    "subnet-f5cfda97",  // Public Subnet (AZ2)
  ]
}

variable "openflow_subnets" {
  description = "List of subnet IDs for the openflow environment"
  type        = list(string)
  default     = [
    "subnet-00ab0b74fb8f1b3ee",  // Private Subnet (AZ2)
    "subnet-082861aaa73e611be",  // Private Subnet (AZ4)
    "subnet-0cf53551189e58098",  // Public Subnet (AZ2)
    "subnet-0ce485b0562ddd16e",  // Public Subnet (AZ2)
  ]
}
variable "account_name" {
  description = "The name of the AWS account."
  type        = string
  default     = "Evertrue" # You can set a default or leave it out to make it mandatory.
}

variable "cpu_utilization_threshold" {
  description = "The threshold for the RDS CPU Utilization alarm."
  type        = number
  default     = 85
}

#----------

variable "dynamodb_tables" {
  description = "List of DynamoDB table names"
  type        = list(string)
  default     = [
    "engagements",
    "et-terraform-state-lock",
    "facebook_engagements",
    "sodas-profile",
  ]
}

#----------

variable "load_balancers" {
  description = "A list of classic load balancer names"
  type        = list(string)
  default     = ["dev-apps-frontend", "docker-registry"]
}

variable "application_load_balancer" {
  description = "The name of the application load balancer"
  type        = string
  default     = "prod-public"
}

variable "sns_topic_arn" {
  description = "ARN of the SNS topic to which alarms will be sent"
  type        = string
  default     = "arn:aws:sns:us-east-1:037590317780:monitoring-alarms"
}

#-----------


variable "sqs_queues" {
  description = "List of SQS queues ARNs"
  type        = list(string)
  default     = [
"alex-events-storm",
"cloudtrail",
"dev-bob-social-change-notify",
"dev-contacts-big-change-notify",
"dev-contacts-change-notify",
"dev-contacts-geocode-notify",
"dev-contacts-list-change-notify",
"dev-contacts-list-membership-change-notify",
"dev-contacts-news-mentions",
"dev-contacts-refresh-notify",
"dev-email",
"dev-events-storm",
"dev-hadoop-job-failed",
"dev-hadoop-job-queue",
"dev-hai-contacts-change-notify",
"dev-hai-contacts-geocode-notify",
"dev-hai-contacts-read-notify",
"dev-hai-contacts-refresh-notify",
"dev-hai-email",
"dev-hammond-activity-notify",
"dev-hammond-sessions-change-notify",
"dev-hammond-users-change-notify",
"dev-identity-wal",
"dev-sessions-notify",
"dev-social-change-notify",
"dev-suggested-update-notify",
"dev-users-change-notify",
"dev_payments_exporter",
"dev_payments_exporter_hammond",
"dev_volunteers_default",
"dev_volunteers_default_hammond",
"dev_volunteers_default_tom",
"dev_volunteers_export",
"dev_volunteers_hammond_default",
"dev_volunteers_tom",
"development_payments_exporter_default",
"development_payments_exporter_hammond",
"mark-hadoop-job-queue",
"prod-activity-notify",
"prod-cassandra-spark-test",
"prod-contacts-big-change-notify",
"prod-contacts-change-notify",
"prod-contacts-geocode-notify",
"prod-contacts-list-change-notify",
"prod-contacts-list-membership-change-notify",
"prod-contacts-news-mentions",
"prod-contacts-read-notify",
"prod-contacts-refresh-notify",
"prod-email",
"prod-events-storm",
"prod-hadoop-job-failed",
"prod-hadoop-job-queue",
"prod-social-change-notify",
"prod-suggested-update-notify",
"prod_volunteers_default",
"prod_volunteers_export",
"production_payments_exporter",
"production_volunteers_default",
"stage-activity-notify",
"stage-contacts-big-change-notify",
"stage-contacts-change-notify",
"stage-contacts-geocode-notify",
"stage-contacts-list-change-notify",
"stage-contacts-list-membership-change-notify",
"stage-contacts-news-mentions",
"stage-contacts-refresh-notify",
"stage-email",
"stage-events-storm",
"stage-hadoop-job-failed",
"stage-hadoop-job-queue",
"stage-organizations-purge",
"stage-sessions-notify",
"stage-social-change-notify",
"stage-suggested-update-notify",
"stage_volunteers_default",
"stage_volunteers_export",
"staging_payments_exporter",
"volunteers_default"
  ]
}
#----------

variable "lambda_functions" {
  description = "List of Lambda function names"
  type        = list(string)
  default     = [
    "prod_bw_dm_get_tasks",
    "ExportStatusCheckQueueSize",
    "gitrdone-github-auth",
    "VoyagerCronofyUserCleanupStage",
    "gitrdone-github-callback",
    "vault-webhook-vault-sealed",
    "add_HSTS_header_community-web",
    "beaconV1",
    "LogsToElasticsearch_stage-service-logs-pub",
    "prod-bw-dms-run-job",
    "ExportStatusCheck",
    "test-sftp-users-remove-when-done",
    "prod-bw-dms-job-status",
    "api-test",
    "ExportStatusCheckFailures",
    "dms_task_slack_alerts",
    "prod-bw-snowflake-ext-refresh",
    "prod_bw_dms_get_tasks2",
    "SparkStatusCheckProduction",
    "http_redirect_with_headers",
    "SparkStatusCheckStaging",
    "SuggestionsEsHandler",
    "vpc-flowlogs-handler-dev-run",
    "add_custom_CF_viewer_response_headers",
    "sf-updater-stage-update-org-stats",
    "sf-updater-production-update-org-stats",
    "SparkStatusCheck"
  ]
}

data "aws_caller_identity" "current" {}

resource "aws_s3_bucket" "flow_logs" {
  bucket = "${data.aws_caller_identity.current.account_id}-flowlogs"
  acl    = "private"

  tags = {
    "ManagedBy" = "Terraform"
    "CreatedBy" = "Michael D"
  }
}

resource "aws_s3_bucket_lifecycle_configuration" "flow_logs_lifecycle" {
  bucket = aws_s3_bucket.flow_logs.id

  rule {
    id      = "LogsExpiration"
    status  = "Enabled"
    
    expiration {
      days = var.retention_days
    }
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "sse_config" {
  bucket = aws_s3_bucket.flow_logs.id
  
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

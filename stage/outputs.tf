output "vpc_id" {
  value = "${module.vpc.vpc_id}"
}

output "private_subnet_ids" {
  value = ["${module.vpc.private_subnet_ids}"]
}

output "default_sg_id" {
  value = "${module.vpc.default_sg_id}"
}

output "default-limited_sg_id" {
  value = "${module.vpc.default-limited_sg_id}"
}

output "stage-prod_peering_connection_id" {
  value = "${aws_vpc_peering_connection.stage-prod.id}"
}

output "mesos-slave_sg_id" {
  value = "${var.mesos-slave_sg_id}"
}

output "monitor_sg_id" {
  value = "${module.vpc.monitor_sg_id}"
}

output "contacts-cassandra_sg_id" {
  value = "${var.contacts-cassandra_sg_id}"
}

output "auth-api_sg_id" {
  value = "${var.auth-api_sg_id}"
}

output "sodas-api_sg_id" {
  value = "${var.sodas-api_sg_id}"
}

output "storm-nimbus_sg_id" {
  value = "${var.storm-nimbus_sg_id}"
}

output "spark_sg_id" {
  value = "${var.spark_sg_id}"
}

output "storm-supervisor_sg_id" {
  value = "${var.storm-supervisor_sg_id}"
}

output "internal-lb_sg_id" {
  value = "${var.internal-lb_sg_id}"
}

output "web-services_sg_id" {
  value = "${aws_security_group.web-services.id}"
}

# Security Group: web-services

resource "aws_security_group" "web-services" {
  name = "${var.env}-web-services"
  description = "Web Services Group (${var.env})"
  vpc_id = "${module.vpc.vpc_id}"

  tags {
    Env = "${var.env}"
    Name = "${var.env}-web-services"
  }
}

# lb: tcp/8080
# This group is defined in the lb module for inheritence reasons

# SECURITY GROUP EGRESS RULE
resource "aws_security_group_rule" "web-services_egress_all" {
  security_group_id = "${aws_security_group.web-services.id}"

  type = "egress"
  from_port = 0
  to_port = 0
  protocol = "-1"
  cidr_blocks = ["0.0.0.0/0"]
}

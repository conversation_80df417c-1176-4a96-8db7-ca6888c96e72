terraform {
  backend "s3" {
    bucket = "et-terraform"
    key = "stage/services/mesos-singularity.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "env" {
  backend = "s3"
  config {
    bucket = "et-terraform"
    region = "us-east-1"
    key = "stage.tfstate"
  }
}


module "mesos-singularity" {
  source = "../../../modules/mesos-singularity"

  env = "${var.env}"
  env_icase = "${var.env_icase}"
  agent_node_count     = 3
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"
  key_pair_id          = "${var.key_pair_id}"
  iam_instance_profile = "${var.env}-singularity-executor"

  monitor_sg_id            = "${data.terraform_remote_state.env.monitor_sg_id}"
  default_sg_id    = "${data.terraform_remote_state.env.default_sg_id}"

  private_subnet_ids = "${data.terraform_remote_state.env.private_subnet_ids}"
  # vpc_id             = "vpc-1e45b27b"
  vpc_id               = "${data.terraform_remote_state.env.vpc_id}"
}

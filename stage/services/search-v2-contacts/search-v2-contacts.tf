terraform {
  backend "s3" {
    bucket = "et-terraform"
    key = "stage/services/search-v2-contacts.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "env" {
  backend = "s3"
  config {
    bucket = "et-terraform"
    region = "us-east-1"
    key = "${var.env}.tfstate"
  }
}


module "search-v2-cluster" {
  source = "../../../modules/search-contacts"

  env = "${var.env}"
  data_node_count      = 3
  chef_user_name       = "${var.chef_user_name}"
  chef_client_key_file = "${var.chef_client_key_file}"
  key_pair_id          = "${var.key_pair_id}"

  mesos-slave_sg_id        = "${data.terraform_remote_state.env.mesos-slave_sg_id}"
  monitor_sg_id            = "${data.terraform_remote_state.env.monitor_sg_id}"
  contacts-cassandra_sg_id = "${data.terraform_remote_state.env.contacts-cassandra_sg_id}"
  auth-api_sg_id           = "${data.terraform_remote_state.env.auth-api_sg_id}"
  sodas-api_sg_id          = "${data.terraform_remote_state.env.sodas-api_sg_id}"
  storm-nimbus_sg_id       = "${data.terraform_remote_state.env.storm-nimbus_sg_id}"
  spark_sg_id              = "${data.terraform_remote_state.env.spark_sg_id}"
  mesos-slave_sg_id        = "${data.terraform_remote_state.env.mesos-slave_sg_id}"
  storm-supervisor_sg_id   = "${data.terraform_remote_state.env.storm-supervisor_sg_id}"
  default-limited_sg_id    = "${data.terraform_remote_state.env.default-limited_sg_id}"

  private_subnet_ids = "${data.terraform_remote_state.env.private_subnet_ids}"
  vpc_id             = "${data.terraform_remote_state.env.vpc_id}"
}

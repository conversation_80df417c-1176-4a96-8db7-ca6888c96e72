data "terraform_remote_state" "env" {
  backend = "s3"
  config {
    bucket = "et-terraform"
    region = "us-east-1"
    key = "stage.tfstate"
  }
}

module "vpn" {
  source = "../../../modules/chef_aws_cluster"

  name = "vpn"
  env = "stage"
  instance_count = 1
  instance_type = "r3.xlarge"
  current_ami = "ami-914ced87"
  subnet_id = "${data.terraform_remote_state.env.private_subnet_ids.1}"

  key_pair_id = "chef_default"

  security_group_ids = [
    "${data.terraform_remote_state.env.default-limited_sg_id}",
    "sg-13201e6b"
  ]

  chef_provisioner_key = "~/.chef/eherot.pem"
  user_name = "eherot"

  job = "elasticsearch-data"
  type = "data_storage"
}

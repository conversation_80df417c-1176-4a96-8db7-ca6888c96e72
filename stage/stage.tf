terraform {
  backend "s3" {
    bucket = "et-terraform"
    key = "stage.tfstate"
    region = "us-east-1"
  }
}

data "terraform_remote_state" "master_state" {
  backend = "s3"
  config {
    bucket = "${var.tf_s3_bucket}"
    region = "${var.region}"
    key = "${var.master_state_file}"
  }
}

# data "prod_remote_state" "prod_state" {
#   backend = "s3"
#   config {
#     bucket = "${var.tf_s3_bucket}"
#     region = "${var.region}"
#     key = "terraform-prod.tfstate"
#   }
# }

module "vpc" {
  source = "../modules/vpc"

  cidr = "${var.cidr}"
  enable_dns_support = "${var.enable_dns_support}"
  enable_dns_hostnames = "${var.enable_dns_hostnames}"
  public_ranges = "${var.public_ranges}"
  private_ranges = "${var.private_ranges}"
  office_external_netblocks = "${var.office_external_netblocks}"
  office_cidr_block = "${var.office_cidr_block}"
  road_warrior_vpn_cidr_blocks = "${var.road_warrior_vpn_cidr_blocks}"
  road_warrior_vpn_instance_id = "${var.road_warrior_vpn_instance_id}"
  nat_subnet_id = "${module.vpc.public_subnet_ids[1]}"
  internal_lb_sg_id = "sg-7dd94204"
  pluralize_default-limited = "s"
  propagating_vgws = ["${aws_vpn_gateway.office.id}"]
  azs = "${var.azs}"
  env = "${var.env}"
  env_icase = "${var.env_icase}"
}

# stage-prod PEERING

resource "aws_vpc_peering_connection" "stage-prod" {
  vpc_id = "${module.vpc.vpc_id}"
  peer_vpc_id = "${var.peer_vpc_id}"
  auto_accept = "true"
  peer_owner_id = "${var.account_number}"

  tags {
    Name = "stage-prod-peering"
  }
}

resource "aws_route" "stage-prod-public-peering" {
  route_table_id = "${module.vpc.public_route_table_id}"
  destination_cidr_block = "${var.peer_vpc_cidr_block}"
  vpc_peering_connection_id = "${aws_vpc_peering_connection.stage-prod.id}"
}

resource "aws_route" "stage-prod-private-peering" {
  route_table_id = "${module.vpc.private_route_table_id}"
  destination_cidr_block = "${var.peer_vpc_cidr_block}"
  vpc_peering_connection_id = "${aws_vpc_peering_connection.stage-prod.id}"
}

# VPN

resource "aws_vpn_gateway" "office" {
  vpc_id = "${module.vpc.vpc_id}"
  availability_zone = "us-east-1c"

  tags {
    Name = "${var.env}-office-vpg"
    Env = "${var.env}"
  }
}

# Office VPN tunnel (r-rtb-3e7f965b3062066622)
resource "aws_route" "office_vpn_private_tunnel" {
  route_table_id = "${module.vpc.private_route_table_id}"
  destination_cidr_block = "${var.office_cidr_block}"
  # depends_on = ["aws_route_table.private"]
  gateway_id = "${aws_vpn_gateway.office.id}"
}

resource "aws_route" "office_vpn_public_tunnel" {
  route_table_id = "${module.vpc.public_route_table_id}"
  destination_cidr_block = "${var.office_cidr_block}"
  # depends_on = ["aws_route_table.public"]
  gateway_id = "${aws_vpn_gateway.office.id}"
}

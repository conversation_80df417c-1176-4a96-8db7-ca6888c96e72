locals {
  loadbalancers = {
    # template = {
    #   name                  = "template"
    #   load_balancer_type    = "application"
    #   vpc_id                = module.vpc["template"].vpc_id
    #   subnets               = module.vpc["template"].public_subnets
    #   create_security_group = false
    #   security_groups       = [module.security_group["template-lb"].security_group_id, module.security_group["template-self"].security_group_id]
    #   access_logs = {
    #     enabled = true
    #     bucket  = "et-template-tier"
    #   }
    #   target_groups = [
    #     {
    #       name                 = "template-server"
    #       backend_protocol     = "HTTP"
    #       backend_port         = 80
    #       deregistration_delay = 10
    #       target_type          = "ip"
    #       health_check = {
    #         enabled             = true
    #         interval            = 5
    #         path                = "/health"
    #         port                = "traffic-port"
    #         healthy_threshold   = 2
    #         unhealthy_threshold = 2
    #         timeout             = 2
    #         protocol            = "HTTP"
    #         matcher             = "200-399"
    #       }
    #     }
    #   ]
    #   http_tcp_listeners = [
    #     {
    #       port        = 80
    #       protocol    = "HTTP"
    #       action_type = "redirect"
    #       redirect = {
    #         port        = "443"
    #         protocol    = "HTTPS"
    #         status_code = "HTTP_301"
    #       }
    #     }
    #   ]
    #   https_listeners = [
    #     {
    #       port     = 443
    #       protocol = "HTTPS"
    #       certificate_arn    = data.aws_acm_certificate.pledgemine_com.arn
    #       target_group_index = 0
    #       tags               = "${merge(var.commonTagsGlobal, { Ticket = "ET-#####", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "19700101" })}"
    #     }
    #   ]
    #   idle_timeout = 600
    #   tags         = "${merge(var.commonTagsGlobal, { Ticket = "ET-#####", Creator = "<EMAIL>", Tier = "Prod", Component = "App ALB", CreateDate = "19700101" })}"
    # }
  }
}

module "alb" {
  source  = "terraform-aws-modules/alb/aws"
  version = "~> 8.7.0"

  for_each = local.loadbalancers

  name                  = each.key
  load_balancer_type    = try(each.value.load_balancer_type, "application")
  vpc_id                = try(each.value.vpc_id, "")
  subnets               = try(each.value.subnets, [])
  internal              = try(each.value.internal, false)
  create_security_group = try(each.value.create_security_group, false)
  security_groups       = try(each.value.security_groups, [])
  access_logs           = try(each.value.access_logs, {})
  target_groups         = try(each.value.target_groups, [])
  https_listeners       = try(each.value.https_listeners, [])
  http_tcp_listeners    = try(each.value.http_tcp_listeners, [])
  idle_timeout          = try(each.value.idle_timeout, 60)
  tags                  = try(each.value.tags, {})
}

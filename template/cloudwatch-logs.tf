locals {
  log_groups = {
    # "template" = {
    #   retention_in_days = 7
    #   log_group_class   = "STANDARD"

    #   tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-#####", Creator = "<EMAIL>", Tier = "Prod", Component = "Template Log Group", CreateDate = "19700101", "AmazonECSManaged" = "" })}"
    # }
  }
}

module "log_group" {
  source  = "terraform-aws-modules/cloudwatch/aws//modules/log-group"
  version = "~> 5.0"

  for_each = local.log_groups

  name              = each.key
  retention_in_days = try(each.value.retention_in_days, 7)
  kms_key_id        = try(each.value.kms_key_id, null)
  log_group_class   = try(each.value.log_group_class, "INFREQUENT_ACCESS")
  tags              = try(each.value.tags, {})
}

################################################################################
################################################################################
# Data Objects
################################################################################
################################################################################

data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

################################################################################
# Latest Ubuntu Pro 20.04 AMI (10 years of updates, expires 2030)
################################################################################
data "aws_ami" "ubuntu_pro_20_04" {
  most_recent = true
  owners      = ["099720109477"]

  filter {
    name   = "name"
    values = ["ubuntu-pro-server/images/hvm-ssd/ubuntu-focal-20.04-amd64-pro-server*"]
  }
}

################################################################################
# IAM objects
################################################################################
# RDS IAM Role Trust Policy
data "aws_iam_policy_document" "rds_custom_trust_policy" {
  statement {
    effect  = "Allow"
    actions = ["sts:AssumeRole"]

    # Aurora condiditon
    # condition {
    #   test     = "StringEquals"
    #   variable = "aws:SourceArn"
    #   values   = ["${module.aurora_cluster["prod"].cluster_arn}"]
    # }

    # Regular RDS condiditon
    # condition {
    #   test     = "StringEquals"
    #   variable = "aws:SourceArn"
    #   values   = ["${module.db["prod"].db_instance_arn}", "${module.db["prod"].db_option_group_arn}"]
    # }

    principals {
      type        = "Service"
      identifiers = ["rds.amazonaws.com"]
    }
  }
}

# ProdS3Access
data "aws_iam_policy_document" "templateS3Access" {
  statement {
    actions = [
      "s3:ListAllMyBuckets",
      "s3:GetBucketLocation",
    ]

    resources = [
      "arn:aws:s3:::*",
    ]
  }
  statement {
    actions = [
      "s3:ListBucket",
    ]

    resources = [
      "arn:aws:s3:::${module.s3_bucket["et-template-tier"].s3_bucket_id}",
    ]
  }
  statement {
    actions = [
      "s3:*",
    ]

    resources = [
      "arn:aws:s3:::${module.s3_bucket["et-template-tier"].s3_bucket_id}",
      "arn:aws:s3:::${module.s3_bucket["et-template-tier"].s3_bucket_id}/*",
    ]
  }
}

################################################################################
# SSL Private keys
###############################################################################
data "aws_secretsmanager_secret_version" "template_evertrue_com_acme_reg_private_key_current" {
  secret_id = module.secrets-manager["template_evertrue_com_acme_reg_private_key"].secret_ids.template_evertrue_com_acme_reg_private_key
}

data "aws_secretsmanager_secret_version" "template_evertrue_com_cert_private_key_current" {
  secret_id = module.secrets-manager["template_evertrue_com_cert_private_key"].secret_ids.template_evertrue_com_cert_private_key
}

################################################################################
# SSL Public keys
################################################################################
data "tls_public_key" "template_evertrue_com_acme_reg_public_key_current" {
  private_key_pem = jsondecode(data.aws_secretsmanager_secret_version.template_evertrue_com_acme_reg_private_key_current.secret_string)["reg_key"]
}

locals {
  iam-users = {
    # template_tier = {
    #   create_iam_access_key = true
    #   policy_arns           = [module.iam_policy_from_data_source["TemplateS3Access"].arn]
    # }
  }
}

module "iam_user" {
  source = "terraform-aws-modules/iam/aws//modules/iam-user"

  for_each = local.iam-users

  name                          = each.key
  create_iam_user_login_profile = false
  create_iam_access_key         = try(each.value.create_iam_access_key, false)
  policy_arns                   = try(each.value.policy_arns, [])
}

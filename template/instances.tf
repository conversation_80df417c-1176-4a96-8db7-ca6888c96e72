locals {
  instances = {
    # template_host = {
    #   ami                    = data.aws_ami.ubuntu_pro_20_04.id
    #   instance_type          = "t3a.large"
    #   key_name               = module.key_pair["template_tier_cloud_engineering"].key_pair_name
    #   iam_instance_profile   = aws_iam_instance_profile.this["default_instance_role"].name
    #   vpc_security_group_ids = ["${module.security_group["template-self"].security_group_id}"]
    #   subnet_id              = module.vpc["template"].private_subnets[0]
    #   root_block_device = [
    #     {
    #       volume_type = "gp3"
    #       volume_size = 100
    #       encrypted   = true
    #     }
    #   ]
    #   tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-#####", Creator = "<EMAIL>", Tier = "Stage", Component = "Template Server", CreateDate = "19700101" })}"
    # }
  }
}


module "ec2instance" {
  source = "terraform-aws-modules/ec2-instance/aws"

  for_each = local.instances

  name                   = each.key
  ami                    = try(each.value.ami, "")
  instance_type          = try(each.value.instance_type, "")
  key_name               = try(each.value.key_name, "")
  iam_instance_profile   = try(each.value.iam_instance_profile, "")
  monitoring             = true
  ignore_ami_changes     = true
  vpc_security_group_ids = try(each.value.vpc_security_group_ids, [])
  subnet_id              = try(each.value.subnet_id, "")
  private_ip             = try(each.value.private_ip, null)
  ebs_block_device       = try(each.value.ebs_block_device, [])
  tags                   = try(each.value.tags, {})
}

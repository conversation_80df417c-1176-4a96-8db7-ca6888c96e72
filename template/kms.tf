locals {
  encryption_keys = {
    # "database" = {
    #   description             = "Key for the RDS database"
    #   deletion_window_in_days = 7
    #   enable_key_rotation     = true
    #   is_enabled              = true
    #   key_usage               = "ENCRYPT_DECRYPT"
    #   multi_region            = false

    #   # Policy
    #   enable_default_policy = true
    #   key_owners = [
    #     "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
    #   ]
    #   # get value in the role arn that looks like "28ca95b278a0f2c7" from an AWS Console Session
    #   key_administrators = [
    #     "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/AWSReservedSSO_AWSAdministratorAccess_28ca95b278a0f2c7"
    #   ]
    #   # get value in the role arn that looks like "28ca95b278a0f2c7" from an AWS Console Session
    #   key_users = [
    #     "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/aws-reserved/sso.amazonaws.com/AWSReservedSSO_AWSAdministratorAccess_28ca95b278a0f2c7",
    #     aws_iam_service_linked_role.this["rds.amazonaws.com"].arn
    #   ]
    #   key_service_users = [
    #     aws_iam_service_linked_role.this["rds.amazonaws.com"].arn
    #   ]
    #   key_symmetric_encryption_users         = []
    #   key_hmac_users                         = []
    #   key_asymmetric_public_encryption_users = []
    #   key_asymmetric_sign_verify_users       = []

    #   # Aliases
    #   aliases                 = ["rds-prod-db"] # accepts static strings only
    #   aliases_use_name_prefix = false

    #   tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-#####", Creator = "<EMAIL>", Tier = "Prod", Component = "RDS DB Encryption Key", CreateDate = "********" })}"
    # }
  }
}

module "kms" {
  source = "terraform-aws-modules/kms/aws"

  for_each = local.encryption_keys

  description             = try(each.value.description, "")
  deletion_window_in_days = try(each.value.deletion_window_in_days, 7)
  enable_key_rotation     = try(each.value.enable_key_rotation, true)
  is_enabled              = try(each.value.is_enabled, true)
  key_usage               = try(each.value.key_usage, "ENCRYPT_DECRYPT")
  multi_region            = try(each.value.multi_region, false)

  # Policy
  enable_default_policy                  = try(each.value.enable_default_policy, true)
  key_owners                             = try(each.value.key_owners, [])
  key_administrators                     = try(each.value.key_administrators, [])
  key_users                              = try(each.value.key_users, [])
  key_service_users                      = try(each.value.key_service_users, [])
  key_symmetric_encryption_users         = try(each.value.key_symmetric_encryption_users, [])
  key_hmac_users                         = try(each.value.key_hmac_users, [])
  key_asymmetric_public_encryption_users = try(each.value.key_asymmetric_public_encryption_users, [])
  key_asymmetric_sign_verify_users       = try(each.value.key_asymmetric_sign_verify_users, [])

  # Aliases
  aliases                 = try(each.value.aliases, [])
  computed_aliases        = try(each.value.computed_aliases, {})
  aliases_use_name_prefix = try(each.value.aliases_use_name_prefix, true)

  # Grants
  grants = try(each.value.grants, {})

  tags = try(each.value.tags, {})
}

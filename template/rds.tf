locals {
  dbs = {
    # Postgres example
    # template = {
    #   database_name = "template"
    #   # How soon to apply changes
    #   apply_immediately = true

    #   # Engine Info
    #   engine                      = "aurora-postgresql"
    #   engine_version              = "15.4"
    #   allow_major_version_upgrade = true

    #   # Instance size and storage
    #   instance_class    = "db.t3.medium"
    #   storage_type      = "aurora"
    #   storage_encrypted = true
    #   kms_key_id        = module.kms["database"].key_arn

    #   # DB Auth Settings
    #   master_username = "root"
    #   # password is managed by AWS RDS via AWS Secrets Manager

    #   # Network info
    #   vpc_id                 = module.vpc["template"].vpc_id
    #   port                   = 5432
    #   db_subnet_group_name   = aws_db_subnet_group.database["template"].id
    #   vpc_security_group_ids = ["${module.security_group["template-self"].security_group_id}"]

    #   # Compute
    #   instances = {
    #     "1" = {
    #       instance_class          = "db.t3.medium"
    #       publicly_accessible     = false
    #       db_parameter_group_name = "default.aurora-postgresql15"
    #     }
    #   }

    #   # Maint, backup, monitoring, logs
    #   preferred_maintenance_window    = "Sun:00:00-Sun:03:00"
    #   preferred_backup_window         = "03:00-06:00"
    #   backup_retention_period         = 7
    #   skip_final_snapshot             = false
    #   performance_insights_enabled    = true
    #   monitoring_interval             = 60
    #   create_monitoring_role          = true
    #   monitoring_role_name            = "rds-monitoring-role"
    #   create_cloudwatch_log_group     = true
    #   enabled_cloudwatch_logs_exports = ["postgresql"]

    #   # Database Deletion Protection
    #   deletion_protection = true

    #   # Cluster Parameters
    #   create_db_cluster_parameter_group = true
    #   db_cluster_parameter_group_family = "aurora-postgresql15"

    #   # DB Instance Parameters
    #   create_db_parameter_group = true
    #   db_parameter_group_family = "aurora-postgresql15"

    #   tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-#####", Creator = "<EMAIL>", Tier = "Prod", Component = "RDS DB", CreateDate = "19700101" })}"
    # }
    # MySQL example
    # template = {
    #   # How soon to apply changes
    #   apply_immediately = true

    #   # Engine Info
    #   engine                      = "aurora-mysql"
    #   engine_version              = "8.0"
    #   allow_major_version_upgrade = true

    #   # Instance size and storage
    #   instance_class    = "db.t3.medium"
    #   storage_type      = "aurora"
    #   storage_encrypted = true
    #   kms_key_id        = module.kms["database"].key_arn

    #   # DB Auth Settings
    #   master_username = "root"
    #   # password is managed by AWS RDS via AWS Secrets Manager

    #   # Network info
    #   vpc_id                 = module.vpc["template"].vpc_id
    #   port                   = 5432
    #   db_subnet_group_name   = module.vpc["template"].database_subnet_group
    #   vpc_security_group_ids = ["${module.security_group["template-self"].security_group_id}"]

    #   # Compute
    #   instances = {
    #     "1" = {
    #       instance_class          = "db.t3.medium"
    #       publicly_accessible     = false
    #       db_parameter_group_name = "default.aurora-postgresql15"
    #     }
    #   }

    #   # Maint, backup, monitoring, logs
    #   preferred_maintenance_window    = "Sun:00:00-Sun:03:00"
    #   preferred_backup_window         = "03:00-06:00"
    #   backup_retention_period         = 7
    #   skip_final_snapshot             = false
    #   performance_insights_enabled    = false
    #   monitoring_interval             = 60
    #   create_monitoring_role          = true
    #   monitoring_role_name            = "rds-monitoring-role"
    #   create_cloudwatch_log_group     = true
    #   enabled_cloudwatch_logs_exports = ["audit", "error", "general", "slowquery"]

    #   # Database Deletion Protection
    #   deletion_protection = true

    #   # Cluster Parameters
    #   create_db_cluster_parameter_group = true
    #   db_cluster_parameter_group_family = "aurora-mysql8.0"

    #   # DB Instance Parameters
    #   create_db_parameter_group = true
    #   db_parameter_group_family = "aurora-mysql8.0"

    #   tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-#####", Creator = "<EMAIL>", Tier = "Stage", Component = "RDS DB", CreateDate = "19700101" })}"
    # }
  }
}

module "aurora_cluster" {
  source = "terraform-aws-modules/rds-aurora/aws"

  for_each = local.dbs

  name          = each.key
  database_name = try(each.value.database_name, null)

  # How soon to apply changes
  apply_immediately = try(each.value.apply_immediately, false)

  # Engine Info
  engine                      = try(each.value.engine, "")
  engine_version              = try(each.value.engine_version, "")
  allow_major_version_upgrade = try(each.value.allow_major_version_upgrade, false)

  # Instance size and storage
  instance_class    = try(each.value.instance_class, "")
  allocated_storage = try(each.value.allocated_storage, null)
  storage_type      = try(each.value.storage_type, "aurora")
  storage_encrypted = try(each.value.storage_encrypted, true)
  kms_key_id        = try(each.value.kms_key_id, null)


  # DB Auth Settings
  manage_master_user_password         = true
  master_username                     = try(each.value.master_username, "")
  iam_database_authentication_enabled = try(each.value.iam_database_authentication_enabled, false)

  # Network info
  vpc_id                 = try(each.value.vpc_id, "")
  port                   = try(each.value.port, null)
  create_db_subnet_group = try(each.value.create_db_subnet_group, false)
  db_subnet_group_name   = try(each.value.db_subnet_group_name, "")
  create_security_group  = try(each.value.create_security_group, false)
  vpc_security_group_ids = try(each.value.vpc_security_group_ids, [])

  # Compute
  instances = try(each.value.instances, {})

  # Maint, backup, monitoring, logs
  preferred_maintenance_window    = try(each.value.preferred_maintenance_window, "sun:05:00-sun:06:00")
  preferred_backup_window         = try(each.value.preferred_backup_window, "02:00-03:00")
  backup_retention_period         = try(each.value.backup_retention_period, 1)
  skip_final_snapshot             = try(each.value.skip_final_snapshot, true)
  performance_insights_enabled    = try(each.value.performance_insights_enabled, false)
  monitoring_interval             = try(each.value.monitoring_interval, 0)
  iam_role_name                   = try(each.value.iam_role_name, null)
  create_monitoring_role          = try(each.value.create_monitoring_role, false)
  enabled_cloudwatch_logs_exports = try(each.value.enabled_cloudwatch_logs_exports, [])
  create_cloudwatch_log_group     = try(each.value.create_cloudwatch_log_group, false)

  # Database Deletion Protection
  deletion_protection = true

  # Cluster Parameters
  create_db_cluster_parameter_group     = try(each.value.create_db_cluster_parameter_group, false)
  db_cluster_parameter_group_family     = try(each.value.db_cluster_parameter_group_family, "")
  db_cluster_parameter_group_parameters = try(each.value.db_cluster_parameter_group_parameters, [])

  # DB Instance Parameters
  create_db_parameter_group     = try(each.value.create_db_parameter_group, false)
  db_parameter_group_family     = try(each.value.db_parameter_group_family, "")
  db_parameter_group_parameters = try(each.value.db_parameter_group_parameters, [])

  # Tags
  tags = try(each.value.tags, {})
}

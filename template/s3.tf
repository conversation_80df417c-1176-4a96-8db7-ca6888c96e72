locals {
  buckets = {
    # "et-template-tier" = {
    #   versioning = {
    #     status     = true
    #     mfa_delete = false
    #   }
    #   attach_elb_log_delivery_policy    = true
    #   attach_access_log_delivery_policy = true
    #   server_side_encryption_configuration = {
    #     rule = {
    #       apply_server_side_encryption_by_default = {
    #         sse_algorithm = "aws:kms"
    #       }
    #     }
    #   }
    #   intelligent_tiering = {
    #     general = {
    #       status = "Enabled"
    #       filter = {
    #         prefix = "/"
    #       }
    #       tiering = {
    #         ARCHIVE_ACCESS = {
    #           days = 180
    #         }
    #       }
    #     }
    #   }
    # }
  }
}

module "s3_bucket" {
  source = "terraform-aws-modules/s3-bucket/aws"
  # version = "v3.14.0"

  for_each = local.buckets

  bucket = each.key

  force_destroy             = try(each.value.force_destroy, false)
  acceleration_status       = try(each.value.acceleration_status, null)
  request_payer             = try(each.value.request_payer, null)
  object_lock_enabled       = try(each.value.object_lock_enabled, false)
  object_lock_configuration = try(each.value.object_lock_configuration, {})

  # Bucket policies
  attach_policy                            = try(each.value.attach_policy, false)
  policy                                   = try(each.value.policy, null)
  attach_elb_log_delivery_policy           = try(each.value.attach_elb_log_delivery_policy, false)
  attach_lb_log_delivery_policy            = try(each.value.attach_lb_log_delivery_policy, false)
  attach_access_log_delivery_policy        = try(each.value.attach_access_log_delivery_policy, false)
  attach_deny_insecure_transport_policy    = try(each.value.attach_deny_insecure_transport_policy, false)
  attach_require_latest_tls_policy         = try(each.value.attach_require_latest_tls_policy, false)
  attach_deny_incorrect_encryption_headers = try(each.value.attach_deny_incorrect_encryption_headers, false)
  attach_deny_incorrect_kms_key_sse        = try(each.value.attach_deny_incorrect_kms_key_sse, false)
  allowed_kms_key_arn                      = try(each.value.allowed_kms_key_arn, null)
  attach_deny_unencrypted_object_uploads   = try(each.value.attach_deny_unencrypted_object_uploads, false)

  # S3 Bucket Ownership Controls
  control_object_ownership = try(each.value.control_object_ownership, false)
  object_ownership         = try(each.value.object_ownership, "BucketOwnerEnforced")
  acl                      = try(each.value.acl, null) # "acl" conflicts with "grant" and "owner"

  # Logging, Versioning, Website
  logging    = try(each.value.logging, {})
  versioning = try(each.value.versioning, {})
  website    = try(each.value.website, {})
  cors_rule  = try(each.value.cors_rule, [])

  # Server side Encryption
  server_side_encryption_configuration = try(each.value.server_side_encryption_configuration, {})

  # Lifecycle, Tiering, Metrics
  lifecycle_rule       = try(each.value.lifecycle_rule, [])
  intelligent_tiering  = try(each.value.intelligent_tiering, {})
  metric_configuration = try(each.value.metric_configuration, [])
  tags                 = try(each.value.tags, {})

}

locals {
  secrets = {
    # template_evertrue_com_acme_reg_private_key = {
    #   secrets = {
    #     template_evertrue_com_acme_reg_private_key = {
    #       description             = "Private key for template.evertrue.com ACME (Let's Encrypt) registration"
    #       recovery_window_in_days = 7
    #       secret_string           = "Fake initial value"

    #     }
    #   }
    #   tags = {
    #     Owner       = "DevOps team"
    #     Environment = "Dev"
    #     Terraform   = true
    #   }
    # }
    # template_evertrue_com_cert_private_key = {
    #   secrets = {
    #     template_evertrue_com_cert_private_key = {
    #       description             = "Private key for template.evertrue.com SSL certificate"
    #       recovery_window_in_days = 7
    #       secret_string           = "Fake initial value"

    #     }
    #   }
    #   tags = {
    #     Owner       = "DevOps team"
    #     Environment = "Dev"
    #     Terraform   = true
    #   }
    # }
    # template_db_user_password = {
    #   secrets = {
    #     template_db_user_password = {
    #       description             = "Postgres password for the template user"
    #       recovery_window_in_days = 7
    #       secret_string           = "Fake initial value"

    #     }
    #   }
    #   tags = {
    #     Owner       = "DevOps team"
    #     Environment = "Prod"
    #     Terraform   = true
    #   }
    # }
  }
}

module "secrets-manager" {
  source  = "lgallard/secrets-manager/aws"
  version = "~> 0.8"

  for_each = local.secrets

  secrets   = try(each.value.secrets, {})
  unmanaged = true
  tags      = try(each.value.tags, {})
}

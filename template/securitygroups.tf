locals {
  security_groups = {
    # template-self = {
    #   description = "Security group for the template env allowing all members access to all ports"
    #   vpc_id      = module.vpc["template"].vpc_id
    #   ingress_with_self = [
    #     {
    #       rule = "all-all"
    #     }
    #   ]
    #   egress_with_cidr_blocks = [
    #     {
    #       protocol    = "-1"
    #       from_port   = 0
    #       to_port     = 0
    #       cidr_blocks = "0.0.0.0/0"
    #       description = "Egress Rule"
    #     }
    #   ]
    #   tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-#####", Creator = "<EMAIL>", Tier = "Prod", Component = "Self SG", CreateDate = "19700101" })}"
    # }
    # template-lb = {
    #   description         = "Security group for the template env load blancers allowing inbound access to 80, 443"
    #   vpc_id              = module.vpc["template"].vpc_id
    #   ingress_cidr_blocks = ["0.0.0.0/0"]
    #   ingress_rules       = ["http-80-tcp", "https-443-tcp"]
    #   egress_with_cidr_blocks = [
    #     {
    #       protocol    = "-1"
    #       from_port   = 0
    #       to_port     = 0
    #       cidr_blocks = "0.0.0.0/0"
    #       description = "Egress Rule"
    #     }
    #   ]
    #   tags = "${merge(var.commonTagsGlobal, { Ticket = "ET-#####", Creator = "<EMAIL>", Tier = "Prod", Component = "Template LB SG", CreateDate = "19700101" })}"
    # }
  }
}

module "security_group" {
  source = "terraform-aws-modules/security-group/aws"

  for_each = local.security_groups

  name                     = each.key
  description              = each.value.description
  vpc_id                   = each.value.vpc_id
  ingress_cidr_blocks      = try(each.value.ingress_cidr_blocks, [])
  ingress_rules            = try(each.value.ingress_rules, [])
  ingress_with_cidr_blocks = try(each.value.ingress_with_cidr_blocks, [])
  ingress_with_self        = try(each.value.ingress_with_self, [])
  egress_with_cidr_blocks  = try(each.value.egress_with_cidr_blocks, [])
  egress_with_self         = try(each.value.egress_with_self, [])
  tags                     = try(each.value.tags, {})
}

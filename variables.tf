variable "master_state_file" {}
variable "env" {}
variable "env_icase" {}
variable "cidr" {}
variable "road_warrior_vpn_cidr_blocks" {
  default = ["************/24","************/24"]
}
variable "road_warrior_vpn_instance_id" {}

variable "enable_dns_support" {
  default = "true"
}

variable "enable_dns_hostnames" {
  default = "true"
}

variable "public_ranges" { } 
variable "private_ranges" { } 

variable "region" {
  default = "us-east-1"
}

variable "tf_s3_bucket" {
  default = "et-terraform"
}

variable "azs" {
  default = ["us-east-1a","us-east-1b","us-east-1c","us-east-1d"]
}

variable "office_cidr_block" {
  default = "**********/17"
}

variable "office_external_netblocks" {
  default = ["**************/30", "*************/30"]
}

variable "account_number" {
  type = "string"
  default = "************"
}

variable "peer_vpc_id" {}
variable "peer_vpc_cidr_block" {}

# variable "subnet_ids" {
#   type = "map"
# }

variable "mesos-slave_sg_id" {}
variable "contacts-cassandra_sg_id" {}
variable "auth-api_sg_id" {}
variable "sodas-api_sg_id" {}
variable "storm-supervisor_sg_id" {}
variable "storm-nimbus_sg_id" {}
variable "spark_sg_id" {}
variable "internal-lb_sg_id" {}
